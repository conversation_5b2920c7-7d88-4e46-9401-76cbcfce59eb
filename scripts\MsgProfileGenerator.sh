#!/bin/bash
# Set application variables  
APP_HOME=D:\RabbitMQ\Consumer\
CLASSPATH=${APP_HOME}/target/SFTPSend-1.0-SNAPSHOT.jar:${APP_HOME}/target/*
PROFILES_DIR=${APP_HOME}/profiles/generatedProfiles
# Create profiles directory if it doesn't exist
mkdir -p "${PROFILES_DIR}"
# Check if required arguments are provided
if [ $# -lt 4 ]; then
    echo "Usage: $0 <schema> <tableName> <outputFileName> <isBinary>"
    echo "Example: $0 NDUTLD SPINDLE_TEST SPINDLE_TEST.pro true"
    exit 1
fi

SCHEMA=DB2SYS
TABLE_NAME=WASHCANDELA
OUTPUT_FILE=$PROFILES_DIR
IS_BINARY=False
echo "Generating profile for ${SCHEMA}.${TABLE_NAME} to ${OUTPUT_FILE}..."
# Run the MsgProfileGenerator
java -cp "${CLASSPATH}" wdc.disk.ecs.apps.MQUpload.generator.MsgProfileGenerator "${SCHEMA}" "${TABLE_NAME}" "${OUTPUT_FILE}" "${IS_BINARY}"

