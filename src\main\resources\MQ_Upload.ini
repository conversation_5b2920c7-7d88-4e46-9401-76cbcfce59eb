MQUpload_test {
    // File monitoring configurations
    PDS_DIR=C:\opt\ecs\storage\mq\pds\,  // Directory to monitor for incoming files
    BINARY_FORMAT=false,              // Set to true for binary files, false for ASCII files
    
    // RabbitMQ configurations
    MONITOR_INTERVAL=10,              // Monitoring interval in seconds
    RABBITMQ_HOST=*************,          // RabbitMQ server hostname
    RABBITMQ_PORT=5672,              // RabbitMQ AMQP port
    RABBITMQ_USERNAME=guest,          // RabbitMQ username
    RABBITMQ_PASSWORD=guest,          // RabbitMQ password
    RABBITMQ_EXCHANGE=ecs.direct,  // Exchange name for file uploads
    RABBITMQ_QUEUE=dev_demo,       // Queue name for file uploads
    RABBITMQ_ROUTING_KEY=demo,       // Routing key for messages
    
    // File processor configurations
    MAX_FILES_IN_SENDING=10,          // Maximum number of files being processed simultaneously
    WAITING_FOR_WRITE_ENDED=5000,     // Time to wait for file write completion (milliseconds)
    PACK_SAME_MSG_ID=false,           // Whether to pack files with same message ID
    TO_DIR=C:\opt\ecs\storage\mq\sent\,     // Directory for processed files
    
    // DB Message ID Profile configurations (required for ASCII format)
    DB_MSG_ID_PROFILE=C:\opt\ecs\profiles\pdsMsgId.pro  // Profile for DB Message IDs
    
}

