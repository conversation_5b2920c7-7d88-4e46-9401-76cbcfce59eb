package wdc.disk.ecs.apps.MQUpload.dbcompare;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import wdc.disk.ecs.apps.dbcompare.DBRecordCountComparator;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@ExtendWith(MockitoExtension.class)
public class DBRecordCountComparatorTest {

 
    private DBRecordCountComparator comparator;

    @BeforeEach
    public void setUp() throws IOException, SQLException {
        comparator = new DBRecordCountComparator("src/main/resources/db-compare-config.properties");
    }
    
    @Test
    public void testHourlyComparison() throws SQLException {

        List<DBRecordCountComparator.TableComparisonConfig> tables = new ArrayList<>();
        tables.add(new DBRecordCountComparator.TableComparisonConfig("DB2SYS.TDS_SCAN", "MEAS_TIME", "SPINDLE = 10151"));
        tables.add(new DBRecordCountComparator.TableComparisonConfig("DB2SYS.TDS_CHANNEL_STAT", "MEAS_TIME", "SPINDLE = 10151"));
        comparator.compareTableCountsHourByHour(tables, 10);
    }
  
    
    @Test
    public void testMain() {
        // Just test that main method doesn't throw exceptions
        // This is a simple smoke test
        try {
            //String[] args = new String[]{"src/main/resources/db-compare-config.properties", "4"};
            String[] args = new String[]{"src/main/resources/db-compare-config.properties"};
            DBRecordCountComparator.main(args);
            // If we get here without exception, test passes
            assertTrue(true);
        } catch (Exception e) {
            fail("Main method threw exception: " + e.getMessage());
        }
    }
}
