package wdc.disk.ecs.apps.MQUpload.processor;

import java.io.File;
import java.io.IOException;
import java.util.List;

import wdc.disk.ecs.apps.MQUpload.model.RabbitMessage;

public interface FileProcessor {
    /**
     * Process a single file and return a list of RabbitMessages
     * 
     * @param file The file to process
     * @return List of RabbitMessage objects
     * @throws IOException if there is an error processing the file
     */
    List<RabbitMessage> processFile(File file) throws IOException;
}