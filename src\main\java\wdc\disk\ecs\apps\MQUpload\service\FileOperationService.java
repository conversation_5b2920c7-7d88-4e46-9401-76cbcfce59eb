package wdc.disk.ecs.apps.MQUpload.service;

import java.io.File;
import java.io.IOException;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.util.Comparator;

import ibm.disk.utility.LogStream;

// Add import at the top if not already present
    import wdc.disk.ecs.apps.SFTPSend.util.CompareTwoFileSequence;

/**
 * Service class for monitoring directories and handling file operations.
 * This class manages file system operations including file movement and status tracking.
 */
public class FileOperationService {
    private String baseDirectory;
    private String sendingDirectory;
    private String sentDirectory;
    private String problemDirectory;
    private LogStream log;
    private static final int MINIMUM_FILE_SIZE = 9;
    private int maxFilesInSending = 100;
    private long waitingForWriteEnded = 10000;     

    public FileOperationService(String baseDirectory, LogStream log, long waitingForWriteEnded, int maxFilesInSending) {
        this.baseDirectory = baseDirectory;
        this.sendingDirectory = baseDirectory + File.separator + "sending";
        this.sentDirectory = baseDirectory + File.separator + "sent";
        this.problemDirectory = baseDirectory + File.separator + "problem";
        this.log = log;
        this.waitingForWriteEnded = waitingForWriteEnded;
        this.maxFilesInSending = maxFilesInSending;
        initializeDirectories();
    }

    public boolean validateDestination(File toDir) {
        if(toDir == null || !toDir.exists() && !toDir.isDirectory()) {
            log.write(LogStream.ERROR_MESSAGE, "To dir is empty or null.");
            return false;
        };
        
        String[] toList = toDir.list();
        if (toList.length >= maxFilesInSending) {
            log.write(LogStream.INFORMATION_MESSAGE, 
                String.format("%d or more files in /sending: %s, not processing files", 
                        maxFilesInSending, toDir.getAbsolutePath()));
            return false;
        }
        return true;
    }
    
    public boolean validateSendingDirDestination() {
        return validateDestination(new File(sendingDirectory));
    }
    
    public void waitForFileWriting() throws InterruptedException {
        if (waitingForWriteEnded > 0) {
            Thread.sleep(waitingForWriteEnded);
        }
    }

    public static boolean isValidFile(File file) {
        return file != null && file.exists() && file.length() >= MINIMUM_FILE_SIZE;
    }

    public void handleInvalidFile(File file) {
        log.write(LogStream.ERROR_MESSAGE, "File is empty or too small: " + file.getName());
        try {
            File problemDir = new File(file.getParent(), "problem");
            if (!problemDir.exists()) {
                problemDir.mkdir();
            }
            File problemFile = new File(problemDir, "EmptyOrTooSmall_" + file.getName());
            if (file.renameTo(problemFile)) {
                log.write(LogStream.INFORMATION_MESSAGE, "Moved to " + problemFile + " successfully");
            }
        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, "Error handling invalid file: " + e.getMessage());
        }
    }

    private void initializeDirectories() {
        createDirectoryIfNotExists(baseDirectory);
        createDirectoryIfNotExists(sendingDirectory);
        createDirectoryIfNotExists(sentDirectory);
        createDirectoryIfNotExists(problemDirectory);
    }

    private void createDirectoryIfNotExists(String directoryPath) {
        File directory = new File(directoryPath);
        if (!directory.exists()) {
            if (directory.mkdirs()) {
                log.write(LogStream.INFORMATION_MESSAGE, "Created directory: " + directoryPath);
            } else {
                log.write(LogStream.ERROR_MESSAGE, "Failed to create directory: " + directoryPath);
            }
        }
    }

    private boolean isFileAccessible(File file) {
        try (FileChannel channel = FileChannel.open(file.toPath(), StandardOpenOption.WRITE)) {
        	// Try to get an exclusive lock without blocking
            FileLock lock = channel.tryLock();
            if (lock != null) {
                lock.release();
                return true;
            }
            return false;
        } catch (IOException e) {
            log.write(LogStream.DEBUG_MESSAGE, 
                "File is being used by another process: " + file.getName());
            return false;
        }
    }

    public void moveFilesToSendingDirectory() {
        File baseDir = new File(baseDirectory);
        File[] files = baseDir.listFiles(file -> 
            file.isFile() && !file.isHidden() && !file.getName().startsWith("."));
        
        if (files == null || files.length == 0) {
            return;
        }

        for (File file : files) {
            try {
                if (!isFileAccessible(file)) {
                    log.write(LogStream.WARNING_MESSAGE, 
                        "Skipping file as it's in use: " + file.getName());
                    continue;
                }
                moveFile(file, sendingDirectory);
                log.write(LogStream.INFORMATION_MESSAGE, 
                    "Moved file to sending directory: " + file.getName());
            } catch (IOException e) {
                log.write(LogStream.ERROR_MESSAGE, 
                    "Failed to move file to sending directory: " + file.getName() + 
                    ". Error: " + e.getMessage());
            }
        }
    }

    public void moveFile(File file, String targetDirectory) throws IOException {
        Path source = file.toPath();
        Path target = Paths.get(targetDirectory, file.getName());

        // If target file exists, append timestamp to filename
        if (Files.exists(target)) {
            String newFileName = file.getName() + "." + System.currentTimeMillis();
            target = Paths.get(targetDirectory, newFileName);
        }

        Files.move(source, target, StandardCopyOption.REPLACE_EXISTING);
    }
    
    /**
     * Gets a sorted array of files from the directory
     */
    public File[] getSortedFiles(File dir) {
        try {
            return Files.walk(dir.toPath())
                .filter(path -> path.toFile().isFile())
                .map(Path::toFile)
                .sorted(Comparator.comparingLong(File::lastModified)
                    .thenComparing((f1, f2) -> {
                        if (f1.lastModified() == f2.lastModified()) {
                            return CompareTwoFileSequence.compareTwoFileIfSameLastModify(
                                f1.getName(), f2.getName()) ? -1 : 1;
                        }
                        return 0;
                    }))
                .toArray(File[]::new);
        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, "Error getting sorted files: " + e.getMessage());
            return new File[0];
        }
    }
    
    /**
     * Gets a sorted array of files from the sending directory
     */
    public File[] getSortedFilesFromSendingDir() {
        return getSortedFiles(new File(sendingDirectory));
    }
    
    /**
     * Moves the processed file to the sent directory
     */
    public void moveToSent(File file) throws IOException {
        moveFile(file, sentDirectory);
        log.write(LogStream.INFORMATION_MESSAGE, "Moved file to processed directory: " + file.getName());
    }

    /**
     * Moves the file to the problem directory
     */
    public void moveToProblem(File file) throws IOException {
        moveFile(file, problemDirectory);
        log.write(LogStream.INFORMATION_MESSAGE, "Moved file to problem directory: " + file.getName());
    }
}