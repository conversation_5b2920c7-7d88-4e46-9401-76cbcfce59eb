
# Producer Configuration
producer:
  # File monitoring configurations
  pds_dir: "C:\\opt\\ecs\\storage\\mq\\"
  binary_format: false
  
  # File processor configurations
  max_files_in_sending: 10
  waiting_for_write_ended: 100
  pack_same_msg_id: false
  to_dir: "C:\\opt\\ecs\\storage\\mq\\sent\\"
  
  # DB Message ID Profile configurations
  db_msg_id_profile: "C:\\Development\\TFS_SourceCode\\ECS\\SZ_GO\\Common\\Dev\\SFTPSend_Hao\\profiles\\pdsMsgId.pro"
  monitor_interval: 10

  housekeeping:
      enabled: true
      interval_hours: 24
      retention_days: 30
      directories:
        - sent

# RabbitMQ Configuration
rabbitmq_own:
  host: "*************"
  port: 5672
  username: "guest"
  password: "guest"
  exchange: "ecs.direct"
  queue_config_path: "C:\\Development\\TFS_SourceCode\\ECS\\SZ_GO\\Common\\Dev\\SFTPSend_Hao\\src\\test\\resources\\test-queue-config.csv"

rabbitmq:
  host: "csf-mp-talend03.ad.shared"
  port: 5672
  username: "mqupload"
  password: "upload66"
  exchange: "ecs.direct"
  queue_config_path: "C:\\Development\\TFS_SourceCode\\ECS\\SZ_GO\\Common\\Dev\\SFTPSend_Hao\\src\\test\\resources\\test-queue-config.csv"


# Consumer Configuration
consumer:
  mybatis_config_path: "C:\\Development\\TFS_SourceCode\\ECS\\SZ_GO\\Common\\Dev\\SFTPSend_Hao\\src\\test\\resources\\mybatis-config.xml"
  parser_class_name: "wdc.disk.ecs.apps.MQUpload.parser.AsciiMessageParser"
  mappings_path: "C:\\Development\\TFS_SourceCode\\ECS\\SZ_GO\\Common\\Dev\\SFTPSend_Hao\\src\\test\\resources\\mapper\\message_mappings.json" 
  profiles_path: "C:\\Development\\TFS_SourceCode\\ECS\\SZ_GO\\Common\\Dev\\SFTPSend_Hao\\profiles\\generatedProfiles"
  queues:
    - name: "309"
      description: "Queue for 309 processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100  # Number of messages to prefetch
    - name: "318"
      description: "Queue for 318 processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "222"
      description: "Queue for 222 processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "223"
      description: "Queue for 223 processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "300"
      description: "Queue for TDS_SCAN processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "301"
      description: "Queue for TDS_CHANNEL_STAT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "302"
      description: "Queue for TDS_HEAD_STAT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "303"
      description: "Queue for TDS_GRADE processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "304"
      description: "Queue for TDS_CLUSTER processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "305"
      description: "Queue for TDS_CLUSTER_DATA processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "306"
      description: "Queue for TDS_SITEMARK_CLUS processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
    - name: "310"
      description: "Queue for TDS_ENG processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000  # milliseconds
      processing_interval: 1000  # milliseconds between processing cycles
      prefetch_count: 100
