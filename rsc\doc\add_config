# Adding New Configuration Properties Guide
## 1. Update ConfigurationProperties Class
```java
    // 1. Add the field with default value if applicable
    private String newProperty = "defaultValue";

    // 2. Add getter
    public String getNewProperty() { return newProperty; }

    // 3. Add setter
    public void setNewProperty(String newProperty) { this.newProperty = newProperty; }
 ```
```

## 2. Update Configuration Files
- Add the new property to rsc/test/rabbitmq.properties for testing
- Update any other property files used in the project
## 3. Update Tests
- Modify `RabbitMQServiceTest.java` :
  - Add mock configuration in setUp()
  - Add test cases for the new property if needed
## 4. Update Configuration Loading
- If the property requires special validation or transformation, update `ConfigurationLoader.java`

## 5. Property Usage Checklist
- Field declaration with appropriate type
- Getter method
- Setter method
- Default value (if needed)
- Property file updates
- Test coverage
- Documentation updates