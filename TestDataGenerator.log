~0 14:44:06 Apr 1, 2025 Exception org.yaml.snakeyaml.scanner.ScannerException occurred, message = while scanning for the next token
found character '\t(TAB)' that cannot start any token. (Do not use \t(TAB) for indentation)
 in 'reader', line 27, column 1:
    	QUEUES = 309/318, 	
    ^

while scanning for the next token
found character '\t(TAB)' that cannot start any token. (Do not use \t(TAB) for indentation)
 in 'reader', line 27, column 1:
    	QUEUES = 309/318, 	
    ^

	at org.yaml.snakeyaml.scanner.ScannerImpl.fetchMoreTokens(ScannerImpl.java:445)
	at org.yaml.snakeyaml.scanner.ScannerImpl.peekToken(ScannerImpl.java:261)
	at org.yaml.snakeyaml.parser.ParserImpl$ParseDocumentEnd.produce(ParserImpl.java:279)
	at org.yaml.snakeyaml.parser.ParserImpl.peekEvent(ParserImpl.java:161)
	at org.yaml.snakeyaml.comments.CommentEventsCollector$1.peek(CommentEventsCollector.java:57)
	at org.yaml.snakeyaml.comments.CommentEventsCollector$1.peek(CommentEventsCollector.java:43)
	at org.yaml.snakeyaml.comments.CommentEventsCollector.collectEvents(CommentEventsCollector.java:136)
	at org.yaml.snakeyaml.comments.CommentEventsCollector.collectEvents(CommentEventsCollector.java:116)
	at org.yaml.snakeyaml.composer.Composer.composeScalarNode(Composer.java:241)
	at org.yaml.snakeyaml.composer.Composer.composeNode(Composer.java:205)
	at org.yaml.snakeyaml.composer.Composer.getNode(Composer.java:131)
	at org.yaml.snakeyaml.composer.Composer.getSingleNode(Composer.java:157)
	at org.yaml.snakeyaml.constructor.BaseConstructor.getSingleData(BaseConstructor.java:178)
	at org.yaml.snakeyaml.Yaml.loadFromReader(Yaml.java:493)
	at org.yaml.snakeyaml.Yaml.load(Yaml.java:434)
	at wdc.disk.ecs.apps.MQUpload.service.ConfigurationLoader.loadConfiguration(ConfigurationLoader.java:37)
	at wdc.disk.ecs.apps.MQUpload.producer.MQUpload.initialize(MQUpload.java:44)
	at wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator.start(TestDataGenerator.java:38)
	at wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator.main(TestDataGenerator.java:75)
~0 14:44:06 Apr 1, 2025 Exception wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException occurred, message = Could not read YAML configuration file: C:\Development\TFS_SourceCode\ECS\SZ_GO\Common\Dev\SFTPSend_Hao\src\test\resources\MQ_Upload.ini
wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException: Could not read YAML configuration file: C:\Development\TFS_SourceCode\ECS\SZ_GO\Common\Dev\SFTPSend_Hao\src\test\resources\MQ_Upload.ini
	at wdc.disk.ecs.apps.MQUpload.service.ConfigurationLoader.loadConfiguration(ConfigurationLoader.java:40)
	at wdc.disk.ecs.apps.MQUpload.producer.MQUpload.initialize(MQUpload.java:44)
	at wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator.start(TestDataGenerator.java:38)
	at wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator.main(TestDataGenerator.java:75)
