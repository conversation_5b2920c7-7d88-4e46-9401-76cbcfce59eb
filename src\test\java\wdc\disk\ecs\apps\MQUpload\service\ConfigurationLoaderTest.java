package wdc.disk.ecs.apps.MQUpload.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException;

public class ConfigurationLoaderTest {

    @TempDir
    Path tempDir;

    @Mock
    private LogStream mockLog;
    
    private ConfigurationLoader loader;
    private File yamlFile;

    @BeforeEach
    void setUp() throws IOException {
        MockitoAnnotations.openMocks(this);
        yamlFile = tempDir.resolve("test-config.yml").toFile();
        // Create necessary directories for tests
        Files.createDirectories(tempDir.resolve("test").resolve("pds"));
        Files.createDirectories(tempDir.resolve("test").resolve("to"));
    }

    // Basic configuration tests
    @Test
    void testLoadConfigurationSuccess() throws IOException, SFTPSendException {
        String yamlContent = createValidConfiguration();
        writeYamlFile(yamlContent);
        loader = new ConfigurationLoader(mockLog, yamlFile.getAbsolutePath());
        loader.loadConfiguration();

        assertValidProducerConfig(loader);
        assertValidRabbitMQConfig(loader);
    }

    @Test
    void testLoadConfigurationWithMinimalConfig() throws IOException, SFTPSendException {
        String yamlContent = createMinimalValidConfiguration();
        writeYamlFile(yamlContent);
        loader = new ConfigurationLoader(mockLog, yamlFile.getAbsolutePath());
        loader.loadConfiguration();

        // Verify minimal configuration is loaded correctly
        assertMinimalProducerConfig(loader);
        assertTrue(loader.getConsumerProperties().getQueues().isEmpty());
    }

    // Validation tests for Producer configuration
    @Test
    void testProducerConfigMissingPdsDir() throws IOException {
        String yamlContent = createValidConfiguration()
            .replace("pds_dir:", "dummy:");  // Remove pds_dir
        writeYamlFile(yamlContent);
        loader = new ConfigurationLoader(mockLog, yamlFile.getAbsolutePath());

        SFTPSendException exception = assertThrows(SFTPSendException.class, 
            () -> loader.loadConfiguration());
        assertEquals("PDS_DIR is required", exception.getMessage());
    }

    @Test
    void testProducerConfigInvalidDirectory() throws IOException {
        // Create a file to block directory creation
        File blockingFile = createBlockingFile();
        String yamlContent = 
            "producer:\n" +
            "  pds_dir: \"" + blockingFile.getAbsolutePath().replace("\\", "\\\\") + "\\\\\"\n" +  // Use blocking file path
            "  to_dir: \"" + tempDir.resolve("test").resolve("to").toString().replace("\\", "\\\\") + "\\\\\"\n" +
            "  max_files_in_sending: 10\n" +
            "  waiting_for_write_ended: 100\n" +
            "  db_msg_id_profile: \"test_profile\"";

        writeYamlFile(yamlContent);
        loader = new ConfigurationLoader(mockLog, yamlFile.getAbsolutePath());

        SFTPSendException exception = assertThrows(SFTPSendException.class, 
            () -> loader.loadConfiguration());
        String expectedMessage = String.format("PDS_DIR path is not a directory: %s\\", blockingFile.getAbsolutePath());
        assertEquals(expectedMessage, exception.getMessage());
    }

    // Helper methods for creating test configurations
    private String createValidConfiguration() {
        return "producer:\n" +
            "  pds_dir: \"" + tempDir.resolve("test").resolve("pds").toString().replace("\\", "\\\\") + "\\\\\"\n" +
            "  binary_format: false\n" +
            "  max_files_in_sending: 10\n" +
            "  waiting_for_write_ended: 100\n" +
            "  to_dir: \"" + tempDir.resolve("test").resolve("to").toString().replace("\\", "\\\\") + "\\\\\"\n" +
            "  db_msg_id_profile: \"" + tempDir.resolve("test").resolve("profile.pro").toString().replace("\\", "\\\\") + "\"\n" +
            "rabbitmq:\n" +
            "  monitor_interval: 10\n" +
            "  host: \"localhost\"\n" +
            "  port: 5672\n" +
            "  username: \"guest\"\n" +
            "  password: \"guest\"\n" +
            "  exchange: \"test.exchange\"";
    }

    private String createMinimalValidConfiguration() {
        return "producer:\n" +
            "  pds_dir: \"" + tempDir.resolve("test").resolve("pds").toString().replace("\\", "\\\\") + "\\\\\"\n" +
            "  to_dir: \"" + tempDir.resolve("test").resolve("to").toString().replace("\\", "\\\\") + "\\\\\"\n" +
            "  max_files_in_sending: 10\n" +
            "  waiting_for_write_ended: 100\n" +
            "  db_msg_id_profile: \"" + tempDir.resolve("test").resolve("profile.pro").toString().replace("\\", "\\\\") + "\"";
    }

    private File createBlockingFile() throws IOException {
        File blockingFile = tempDir.resolve("blocking.txt").toFile();
        Files.write(blockingFile.toPath(), "blocking".getBytes());
        return blockingFile;
    }

    // Helper methods for assertions
    private void assertValidProducerConfig(ConfigurationLoader loader) {
        assertEquals(tempDir.resolve("test").resolve("pds").toString() + "\\", 
                    loader.getProducerProperties().getPdsDir());
        assertFalse(loader.getProducerProperties().isBinaryFormat());
        assertEquals(10, loader.getProducerProperties().getMaxFilesInSending());
        assertEquals(100L, loader.getProducerProperties().getWaitingForWriteEnded());
    }

    private void assertValidRabbitMQConfig(ConfigurationLoader loader) {
        assertEquals("localhost", loader.getRabbitMQProperties().getRabbitMQHosts());
        assertEquals(5672, loader.getRabbitMQProperties().getRabbitMQPort());
        assertEquals("guest", loader.getRabbitMQProperties().getRabbitMQUsername());
        assertEquals("guest", loader.getRabbitMQProperties().getRabbitMQPassword());
    }

    private void assertMinimalProducerConfig(ConfigurationLoader loader) {
        assertEquals(tempDir.resolve("test").resolve("pds").toString() + "\\", 
                    loader.getProducerProperties().getPdsDir());
        assertEquals(10, loader.getProducerProperties().getMaxFilesInSending());
        assertEquals(100L, loader.getProducerProperties().getWaitingForWriteEnded());
    }

    @Test
    void testLoadConfigurationWithoutOptionalSections() throws IOException, SFTPSendException {
        String yamlContent = 
            "producer:\n" +
            "  pds_dir: \"" + tempDir.resolve("test").resolve("pds").toString().replace("\\", "\\\\") + "\\\\\"\n" +
            "  to_dir: \"" + tempDir.resolve("test").resolve("to").toString().replace("\\", "\\\\") + "\\\\\"\n" +
            "  max_files_in_sending: 10\n" +
            "  waiting_for_write_ended: 100\n" +
            "  db_msg_id_profile: \"" + tempDir.resolve("test").resolve("profile.pro").toString().replace("\\", "\\\\") + "\"\n" +
            "rabbitmq:\n" +
            "  monitor_interval: 10\n" +
            "  host: \"localhost\"\n" +
            "  port: 5672\n" +
            "  username: \"guest\"\n" +
            "  password: \"guest\"\n" +
            "  exchange: \"test.exchange\"";

        writeYamlFile(yamlContent);
        loader = new ConfigurationLoader(mockLog, yamlFile.getAbsolutePath());
        loader.loadConfiguration();

        // Verify default values for optional fields
        assertFalse(loader.getProducerProperties().isBinaryFormat());
        assertTrue(loader.getConsumerProperties().getQueues().isEmpty());
    }

    // 1. Remove @Test(expected = SFTPSendException.class) and use assertThrows instead
    // 2. Use void instead of public void for test methods
    
    @Test
    void testLoadConfigurationMissingRequiredFields() throws IOException {
        String yamlContent = 
            "producer:\n" +
            "  binary_format: false\n" +
            "rabbitmq:\n" +
            "  port: 5672";

        writeYamlFile(yamlContent);
        loader = new ConfigurationLoader(mockLog, yamlFile.getAbsolutePath());

        SFTPSendException exception = assertThrows(SFTPSendException.class, 
            () -> loader.loadConfiguration());
        assertEquals("PDS_DIR is required", exception.getMessage());
    }

    @Test
    void testLoadConfigurationInvalidYamlFormat() throws IOException {
        String invalidYamlContent = "invalid: yaml: content: - [";
        writeYamlFile(invalidYamlContent);
        loader = new ConfigurationLoader(mockLog, yamlFile.getAbsolutePath());

        assertThrows(SFTPSendException.class, () -> loader.loadConfiguration());
        verify(mockLog).writeExceptionStack(any(Exception.class));
    }

    @Test
    void testLoadConfigurationFileNotFound() {
        loader = new ConfigurationLoader(mockLog, "nonexistent.yml");
        
        assertThrows(SFTPSendException.class, () -> loader.loadConfiguration());
        verify(mockLog).writeExceptionStack(any(Exception.class));
    }

    @Test
    void testProducerConfigValidation() throws IOException, SFTPSendException {
        String yamlContent = 
            "producer:\n" +
            "  pds_dir: \"" + tempDir.resolve("test").resolve("pds").toString().replace("\\", "\\\\") + "\\\\\"\n" +
            "  to_dir: \"" + tempDir.resolve("test").resolve("to").toString().replace("\\", "\\\\") + "\\\\\"\n" +
            "  max_files_in_sending: 10\n" +
            "  waiting_for_write_ended: 100\n" +
            "  db_msg_id_profile: \"" + tempDir.resolve("test").resolve("profile.pro").toString().replace("\\", "\\\\") + "\"";

        writeYamlFile(yamlContent);
        loader = new ConfigurationLoader(mockLog, yamlFile.getAbsolutePath());
        loader.loadConfiguration(); // Should not throw any exception
    }

    @Test
    void testRabbitMQConfigValidation() throws IOException, SFTPSendException {
        String yamlContent = 
            "producer:\n" +
            "  pds_dir: \"" + tempDir.resolve("test").resolve("pds").toString().replace("\\", "\\\\") + "\\\\\"\n" +
            "  to_dir: \"" + tempDir.resolve("test").resolve("to").toString().replace("\\", "\\\\") + "\\\\\"\n" +
            "  max_files_in_sending: 10\n" +
            "  waiting_for_write_ended: 100\n" +
            "  db_msg_id_profile: \"" + tempDir.resolve("test").resolve("profile.pro").toString().replace("\\", "\\\\") + "\"\n" +
            "rabbitmq:\n" +
            "  monitor_interval: 10\n" +
            "  host: \"localhost\"\n" +
            "  port: 5672\n" +
            "  username: \"guest\"\n" +
            "  password: \"guest\"\n" +
            "  exchange: \"test.exchange\"";

        writeYamlFile(yamlContent);
        loader = new ConfigurationLoader(mockLog, yamlFile.getAbsolutePath());
        loader.loadConfiguration(); // Should not throw any exception
    }

    @Test
    void testDirectoryValidation() throws IOException {
        // Create a file to block directory creation
        File blockingFile = tempDir.resolve("blocking.txt").toFile();
        Files.write(blockingFile.toPath(), "blocking".getBytes());
        
        // Try to use the file path as a directory path
        String invalidPath = blockingFile.getAbsolutePath();
        String yamlContent = 
            "producer:\n" +
            "  pds_dir: \"" + invalidPath.replace("\\", "\\\\") + "\\\\\"\n" +
            "  to_dir: \"" + tempDir.resolve("test").resolve("to").toString().replace("\\", "\\\\") + "\\\\\"\n" +
            "  max_files_in_sending: 10\n" +
            "  waiting_for_write_ended: 100\n" +
            "  db_msg_id_profile: \"test_profile\"";

        writeYamlFile(yamlContent);
        loader = new ConfigurationLoader(mockLog, yamlFile.getAbsolutePath());

        SFTPSendException exception = assertThrows(SFTPSendException.class, 
            () -> loader.loadConfiguration());
        
        // Update the assertion to match the actual error message
        String expectedMessage = String.format("PDS_DIR path is not a directory: %s\\", invalidPath);
        assertEquals(expectedMessage, exception.getMessage());
    }

    @Test
    void testValidationOrder() throws IOException {
        String yamlContent = 
            "producer:\n" +
            "  binary_format: false\n" +  // Missing required fields
            "rabbitmq:\n" +
            "  monitor_interval: 0\n" +   // Invalid value, but shouldn't reach here
            "consumer:\n" +
            "  queues: []";

        writeYamlFile(yamlContent);
        loader = new ConfigurationLoader(mockLog, yamlFile.getAbsolutePath());

        SFTPSendException exception = assertThrows(SFTPSendException.class, 
            () -> loader.loadConfiguration());
        // Should fail on first validation (producer) before reaching rabbitmq
        assertEquals("PDS_DIR is required", exception.getMessage());
    }

    private void writeYamlFile(String content) throws IOException {
        try (FileWriter writer = new FileWriter(yamlFile)) {
            writer.write(content);
            writer.flush();
        }
    }
}

