@echo off
setlocal enabledelayedexpansion

:: 设置工作目录和锁文件路径
set WORK_DIR=D:\WDApps\CandelaCenterApps\ECS\ecs
set LOCK_FILE=C:\temp\LCK..ECS

:: 检查是否已有实例运行
if exist "%LOCK_FILE%" (
    echo ECS is already running
    timeout /t 5 >nul
    exit /b
)

:: 创建锁文件
type nul > "%LOCK_FILE%"

:: 构建CLASSPATH
set CLASSPATH=

:: 先添加debug目录下的jar和zip文件
if exist "%WORK_DIR%\debug\" (
    pushd "%WORK_DIR%\debug"
    for %%f in (*.jar *.zip) do (
        set JARFILE=%WORK_DIR%\debug\%%f
        set CLASSPATH=!CLASSPATH!;!JARFILE!
    )
    popd
)

:: 添加主目录下的jar和zip文件
pushd "%WORK_DIR%"
for %%f in (*.jar *.zip) do (
    set CLASSPATH=!CLASSPATH!;%WORK_DIR%\%%f
)
popd

:: 显示CLASSPATH并启动Java程序
echo !CLASSPATH!
java -noverify -classpath "!CLASSPATH!" ibm.disk.ecs.ecs2000.Ecs2000

:: 删除锁文件
del "%LOCK_FILE%" >nul 2>&1

endlocal