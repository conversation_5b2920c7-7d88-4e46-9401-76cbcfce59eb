@echo off
echo Starting repush operation to RabbitMQ...

:: Set directories
set SOURCE_DIR=C:\opt\ecs\storage\mq\srcPds
set TEMP_DIR=C:\opt\ecs\storage\mq
set CLASSPATH=C:\Development\TFS_SourceCode\ECS\SZ_GO\Common\Dev\SFTPSend_Hao\target\SFTPSend-1.0-SNAPSHOT.jar;D:\RabbitMQ\Dependiencies\*
set CONFIG_FILE=C:\Development\TFS_SourceCode\ECS\SZ_GO\Common\Dev\SFTPSend_Hao\src\test\resources\MQ_Upload.yml
set LOG_FILE=C:\Users\<USER>\Desktop\repush.log

:: Create log directory if it doesn't exist
if not exist logs mkdir logs

:: Copy files from source to temp directory
echo Copying files from %SOURCE_DIR% to %TEMP_DIR%...
xcopy /E /Y "%SOURCE_DIR%\*.*" "%TEMP_DIR%\"

:: Run MQUpload to process the files
echo Running MQUpload to send files to RabbitMQ...
java -Dconfig.block=MQUpload_test -Drabbitmq.block=RabbitMQ_Configurations -Dconsumer.block=Consumer_test ^
     -cp "%CLASSPATH%" wdc.disk.ecs.apps.MQUpload.producer.MQUpload "%CONFIG_FILE%" "%LOG_FILE%"

echo Repush operation completed.
