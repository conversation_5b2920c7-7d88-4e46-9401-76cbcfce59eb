package wdc.disk.ecs.apps.MQUpload.schema;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import wdc.disk.ecs.apps.MQUpload.util.DataTypeMapper;

/**
 * Main parser class that handles loading and parsing of table configurations and message mappings.
 */
public class TableConfigParser {
    
    /**
     * Represents a single column's metadata including type, length, and validation rules.
     */
    public static class ColumnDefinition {
        private String columnName;
        private String dbColumnType;
        private int dbColumnLength;
        private String systemType;
        private int binaryLength;
        private int columnNo;
        private boolean nullable;
        private int startIndex;
        private int valueIndex;
        private boolean uniqueIndex;
        private String length;  

        // Getters and setters
        public String getColumnName() { return columnName; }
        public void setColumnName(String columnName) { this.columnName = columnName; }
        
        public String getDbColumnType() { return dbColumnType; }
        public void setDbColumnType(String dbColumnType) { this.dbColumnType = dbColumnType; }
        
        public int getDbColumnLength() { return dbColumnLength; }
        public void setDbColumnLength(int dbColumnLength) { this.dbColumnLength = dbColumnLength; }
        
        public String getSystemType() { return systemType; }
        public void setSystemType(String systemType) { this.systemType = systemType; }
        
        public int getBinaryLength() { return binaryLength; }
        public void setBinaryLength(int binaryLength) { this.binaryLength = binaryLength; }
        
        public int getColumnNo() { return columnNo; }
        public void setColumnNo(int columnNo) { this.columnNo = columnNo; }
        
        public boolean isNullable() { return nullable; }
        public void setNullable(boolean nullable) { this.nullable = nullable; }
        
        public int getStartIndex() { return startIndex; }
        public void setStartIndex(int startIndex) { this.startIndex = startIndex; }
        
        public int getValueIndex() { return valueIndex; }
        public void setValueIndex(int valueIndex) { this.valueIndex = valueIndex; }
        
        public boolean isUniqueIndex() { return uniqueIndex; }
        public void setUniqueIndex(boolean uniqueIndex) { this.uniqueIndex = uniqueIndex; }

   
        public String getJavaType() {
            return DataTypeMapper.mapToJavaType(systemType);
        }

        /**
         * Converts DB column name to camelCase for Java property names
         */
        public String getCamelCaseName() {
            String[] parts = columnName.toLowerCase().split("_");
            StringBuilder camelCase = new StringBuilder(parts[0]);
            for (int i = 1; i < parts.length; i++) {
                if (parts[i].length() > 0) {
                    camelCase.append(Character.toUpperCase(parts[i].charAt(0)))
                            .append(parts[i].substring(1));
                }
            }
            return camelCase.toString();
        }

       
        public String getLength() { return length; }
        public void setLength(String length) { this.length = length; }

        @Override
        public String toString() {
            return String.format("Column[name=%s, dbType=%s, dbLength=%d, systemType=%s, binaryLength=%d, columnNo=%d, nullable=%b, startIndex=%d, valueIndex=%d, uniqueIndex=%b, length=%s, javaType=%s]",
                    columnName, dbColumnType, dbColumnLength, systemType, binaryLength, columnNo, nullable, startIndex, valueIndex, uniqueIndex, length, getJavaType());
        }
    }

    // Factory method to create ColumnDefinition from profile data
    public static ColumnDefinition createColumnDefinition(String name, String dbType, 
            String systemType, String length, boolean isUnique) {
        ColumnDefinition column = new ColumnDefinition();
        column.setColumnName(name);
        column.setDbColumnType(dbType);
        column.setSystemType(systemType);
        column.setLength(length);
        column.setUniqueIndex(isUnique);
        return column;
    }
    
    
    /**
     * Represents a database table structure with its columns and validation rules.
     */
    public static class TableDefinition {
        private String tableName;
        private String tabCode;     
        private String mqMessageId;   
        private String msgType;       
        private List<ColumnDefinition> columns = new ArrayList<>();
        
        public TableDefinition(String tableName, String tabCode, String mqMessageId, String msgType) {
            this.tableName = tableName;
            this.tabCode = tabCode;
            this.mqMessageId = mqMessageId;
            this.msgType = msgType;
        }
        
        // Keep the existing single-parameter constructor for backward compatibility
        public TableDefinition(String tableName) {
            this(tableName, null, null, null);
        }
        
        // Update getter and setter
        public String getTabCode() { return tabCode; }
        public void setTabCode(String tabCode) { this.tabCode = tabCode; }
        
        public String getMqMessageId() { return mqMessageId; }
        public void setMqMessageId(String mqMessageId) { this.mqMessageId = mqMessageId; }
        
        public String getMsgType() { return msgType; }
        public void setMsgType(String msgType) { this.msgType = msgType; }
        
        public String getTableName() { return tableName; }
        
        public void addColumn(ColumnDefinition column) {
            columns.add(column);
        }
        
        public List<ColumnDefinition> getColumns() {
            return columns;
        }
        
        public ColumnDefinition getColumnByName(String name) {
            return columns.stream()
                    .filter(col -> col.getColumnName().equals(name))
                    .findFirst()
                    .orElse(null);
        }
        
        public List<String> getUniqueIndexColumns() {
            return columns.stream()
                    .filter(ColumnDefinition::isUniqueIndex)
                    .map(ColumnDefinition::getColumnName)
                    .collect(Collectors.toList());
        }
        
        @Override
        public String toString() {
            return "Table[" + tableName + ", tabCode=" + tabCode + 
                   ", mqMessageId=" + mqMessageId + 
                   ", msgType=" + msgType + 
                   ", columns=" + columns.size() + "]";
        }
    } 
    
    private static final int REQUIRED_COLUMN_COUNT = 10; // ColumnName|DBColumnType|UniqueIndex|DBColumnLength|SystemType|BinaryLength|ColumnNo|NULLS|StartIndex|ValueIndex
    
    public static ColumnDefinition parseColumnRow(String line) {
        line = line.substring(1);
        if (line.endsWith("|")) {
            line = line.substring(0, line.length() - 1);
        }
        
        String[] parts = line.split("\\|");
        if (parts.length < REQUIRED_COLUMN_COUNT) return null;
        
        ColumnDefinition column = new ColumnDefinition();
        
        column.setColumnName(parts[0].trim());
        column.setDbColumnType(parts[1].trim());
        column.setDbColumnLength(parseInt(parts[2].trim(), 0));
        column.setSystemType(parts[3].trim());
        column.setBinaryLength(parseInt(parts[4].trim(), 0));
        column.setColumnNo(parseInt(parts[5].trim(), 0));
        
        String nullsValue = parts[6].trim();
        column.setNullable(nullsValue.isEmpty() || "Y".equalsIgnoreCase(nullsValue));
        
        column.setStartIndex(parseInt(parts[7].trim(), 0));
        column.setValueIndex(parseInt(parts[8].trim(), 0));
        
        String uniqueIndexValue = parts[9].trim();  // UniqueIndex column (now last)
        column.setUniqueIndex(uniqueIndexValue != null && !uniqueIndexValue.isEmpty() && uniqueIndexValue.equalsIgnoreCase("Y"));
        
        return column;
    }
    
    
    private static int parseInt(String value, int defaultValue) {
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    
    public static Map<String, TableDefinition> loadMsgMappingFromJsonProfile(String messageMappingsJsonFilePath) throws IOException {
        Map<String, TableDefinition> tableDefinitions = new HashMap<>();
        Gson gson = new Gson();
        InputStream inputStream = null;
        
        try {         
            // If still not found, try as absolute path
            if (inputStream == null) {
                try {
                    inputStream = new FileInputStream(messageMappingsJsonFilePath);
                } catch (FileNotFoundException e) {
                    throw new IOException("Cannot find resource: " + messageMappingsJsonFilePath + 
                                       ". Tried both classpath and file system.");
                }
            }
            
            try (Reader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8)) {
                JsonObject jsonObject = gson.fromJson(reader, JsonObject.class);
                JsonArray profiles = jsonObject.getAsJsonArray("messageProfiles");
                
                if (profiles != null) {
                    for (JsonElement profileElement : profiles) {
                        JsonObject profile = profileElement.getAsJsonObject();
                        String tabCode = profile.get("tabCode").getAsString();
                        String mqMessageId = profile.get("mqMessageId").getAsString();
                        String msgType = profile.get("msgType").getAsString();
                        String tableName = profile.get("tableName").getAsString();
                        
                        TableDefinition tableDef = new TableDefinition(tableName, tabCode, mqMessageId, msgType);
                        tableDefinitions.put(tabCode, tableDef);
                    }
                }
            }
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
        
        return tableDefinitions;
    }
   
    public static String tableDefinitionToJson(TableDefinition table) {
        StringBuilder json = new StringBuilder();
        json.append("{\n");
        json.append("  \"tableName\": \"").append(table.getTableName()).append("\",\n");
        json.append("  \"columns\": [\n");
        
        List<ColumnDefinition> columns = table.getColumns();
        for (int i = 0; i < columns.size(); i++) {
            ColumnDefinition col = columns.get(i);
            json.append("    {\n");
            json.append("      \"name\": \"").append(col.getColumnName()).append("\",\n");
            json.append("      \"type\": \"").append(col.getDbColumnType()).append("\",\n");
            json.append("      \"length\": ").append(col.getDbColumnLength()).append(",\n");
            json.append("      \"systemType\": \"").append(col.getSystemType()).append("\",\n");
            json.append("      \"binaryLength\": ").append(col.getBinaryLength()).append(",\n");
            json.append("      \"columnNo\": ").append(col.getColumnNo()).append(",\n");
            json.append("      \"nullable\": ").append(col.isNullable()).append(",\n");
            json.append("      \"startIndex\": ").append(col.getStartIndex()).append(",\n");
            json.append("      \"valueIndex\": ").append(col.getValueIndex()).append("\n");
            json.append("    }").append(i < columns.size() - 1 ? "," : "").append("\n");
        }
        
        json.append("  ]\n");
        json.append("}");
        return json.toString();
    }
    
    /**
     * Common method to parse profile file and return column definitions
     */
    private static List<ColumnDefinition> parseProfileContent(BufferedReader reader, String profilePath) throws IOException {
        List<ColumnDefinition> columns = new ArrayList<>();
        String line;
        boolean headerFound = false;
        
        // Skip until header line
        while ((line = reader.readLine()) != null) {
            if (line.startsWith("*") && line.contains("ColumnName")) {
                headerFound = true;
                break;
            }
        }
        
        if (!headerFound) {
            throw new IOException("Invalid profile format: header not found in " + profilePath);
        }
        
        // Parse column definitions
        while ((line = reader.readLine()) != null) {
            if (line.trim().isEmpty()) break;
            
            ColumnDefinition column = parseColumnRow(line);
            if (column != null) {
                columns.add(column);
            }
        }
        
        return columns;
    }

    public static List<ColumnDefinition> parseProfileFile(String profilePath) throws IOException {
        try (BufferedReader reader = new BufferedReader(new FileReader(profilePath))) {
            return parseProfileContent(reader, profilePath);
        } catch (FileNotFoundException e) {
            throw new IOException("Profile not found: " + profilePath, e);
        }
    }


    /**
     * Loads column definitions from profile files and updates table definitions
     * @param profileBasePath Base path to profiles directory
     * @param tableDefinitions Map of existing table definitions to be updated
     * @throws IOException if profile reading fails
     */
    public static void loadColumnDefinitions(String profileBasePath, Map<String, TableDefinition> tableDefinitions) throws IOException {
        for (Map.Entry<String, TableDefinition> entry : tableDefinitions.entrySet()) {
            TableDefinition tableDef = entry.getValue();
            String profilePath = String.format("%s/profiles/%s_%s.pro", 
                profileBasePath, 
                tableDef.getMqMessageId(), 
                tableDef.getTabCode());
                
            try (BufferedReader reader = new BufferedReader(new FileReader(profilePath))) {
                List<ColumnDefinition> columns = parseProfileContent(reader, profilePath);
                columns.forEach(tableDef::addColumn);
            } catch (FileNotFoundException e) {
                throw new IOException("Profile not found: " + profilePath, e);
            }
        }
    }
    
    
    public static void main(String[] args) {
//        String tableExample = 
//            "|  ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |\n" +
//            "|------------------------|----------------|-----------------|------------------------|----------------|------------|---------|--------------|-------------|\n" +
//            "|  DTINSERT              |  Timestamp     |  4              |  System.DateTime       |  4             |  0         |  Y      |  0           |  0          |\n" +
//            "|  RESOURCE              |  Char          |  10             |  System.String         |  10            |  1         |         |  0           |  1          |";
//        
//        TableDefinition table = parseTableDefinition("SAMPLE_TABLE", tableExample);
//        System.out.println(table);
//        System.out.println(toJson(table));
        
        try {
            Map<String, TableDefinition> definitions = 
                loadMsgMappingFromJsonProfile("mapper/message_mappings.json");
            
            // Print loaded definitions
            for (Map.Entry<String, TableDefinition> entry : definitions.entrySet()) {
                System.out.println("Table Definition Details:");
                System.out.println("------------------------");
                System.out.println("Tab Code: " + entry.getKey());
                System.out.println("Table Name: " + entry.getValue().getTableName());
                System.out.println("MQ Message ID: " + entry.getValue().getMqMessageId());
                System.out.println("Message Type: " + entry.getValue().getMsgType());
                System.out.println("Number of Columns: " + entry.getValue().getColumns().size());
                System.out.println("------------------------");
                System.out.println(tableDefinitionToJson(entry.getValue()));
                System.out.println("-------------------");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
