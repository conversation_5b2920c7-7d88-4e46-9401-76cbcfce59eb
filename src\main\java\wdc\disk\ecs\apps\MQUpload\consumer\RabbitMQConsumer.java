package wdc.disk.ecs.apps.MQUpload.consumer;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import java.util.HashMap;
import java.util.Map;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import com.rabbitmq.client.Address;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.mapper.MessageDataMapper;
import wdc.disk.ecs.apps.MQUpload.model.ConsumerProperties;
import wdc.disk.ecs.apps.MQUpload.model.QueueMetadataEntry;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMQMetadataLoader;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMQProperties;
import wdc.disk.ecs.apps.MQUpload.parser.MessageParser;
import wdc.disk.ecs.apps.MQUpload.processor.BatchMessageProcessor;
import wdc.disk.ecs.apps.MQUpload.schema.TableSchemaManager;
import wdc.disk.ecs.apps.MQUpload.service.ConfigurationLoader;

/**
 * RabbitMQConsumer Class Documentation
 * 
 * This class is responsible for consuming messages from RabbitMQ queues and processing them.
 * It implements the AutoCloseable interface for proper resource management.
 * 
 * Key features:
 * 1. Connects to RabbitMQ using provided configuration
 * 2. Sets up and manages multiple queues based on configuration
 * 3. Implements a multi-threaded approach for consuming messages
 * 4. Handles batch processing of messages
 * 5. Implements error handling and recovery mechanisms
 * 6. Provides shutdown hooks for graceful termination
 * 
 * Main components:
 * - Connection management: Establishes and maintains connection to RabbitMQ
 * - Channel management: Creates and manages channels for each consumer thread
 * - Queue setup: Declares queues, exchanges, and bindings as per configuration
 * - Message consumption: Implements callback for message delivery and processing
 * - Batch processing: Groups messages into batches for efficient processing
 * - Error handling: Implements retry logic and dead-letter queue mechanism
 * - Shutdown: Provides methods for graceful shutdown of consumers and connections
 * 
 * This class is central to the RabbitMQ consumption process in the application,
 * orchestrating the flow of messages from RabbitMQ to the processing logic.
 */
public class RabbitMQConsumer implements AutoCloseable {
    private final LogStream log;
    private final ConsumerProperties consumerProps;
    private final ConnectionFactory connectionFactory;
    private final RabbitMQProperties mqProps;
    private Connection connection;
    private final RabbitMQMetadataLoader queueMetadataLoader;  
    private final ExecutorService executorService;
    private static final long BATCH_PROCESS_INTERVAL_MS = 5000;
    private static final long RECOVERY_INTERVAL_MS = 5000;

    private static final int DEFAULT_PREFETCH_COUNT = 10;
    //private final ConcurrentHashMap<Thread, Channel> threadChannels = new ConcurrentHashMap<>();
    private volatile boolean isRunning = true;
    private TableSchemaManager schemaManager;
    private final SqlSessionFactory sqlSessionFactory;
    private String logFilePath;
    private final static String DLX_NAME = "ecs.dlx";

    public RabbitMQConsumer(RabbitMQProperties mqProps, 
                           ConsumerProperties consumerProps,
                           String logFilePath,
                           LogStream log, 
                           ConnectionFactory connectionFactory,
                           SqlSessionFactory sqlSessionFactory               
                           ) {
        this.log = log;
        this.consumerProps = consumerProps;
        this.connectionFactory = connectionFactory;
        this.mqProps = mqProps;
        this.sqlSessionFactory = sqlSessionFactory;
        this.logFilePath = logFilePath;
                           
        try {
            this.connection = createConnection(mqProps.getRabbitMQHosts());
        } catch (IOException | TimeoutException e) {
            log.write(LogStream.ERROR_MESSAGE, "Failed to create connection: " + e.getMessage());
            throw new RuntimeException("Failed to create RabbitMQ connection", e);
        }


        try {
            this.queueMetadataLoader = RabbitMQMetadataLoader.loadFromCsv(mqProps.getRabbitMQQueueConfigPath());
        } catch (IOException e) {
            log.write(LogStream.ERROR_MESSAGE, "Failed to load queue configuration: " + e.getMessage());
            throw new RuntimeException("Failed to load queue configuration", e);
        }

        int threadPoolSize = calculateThreadPoolSize();
        this.executorService = Executors.newFixedThreadPool(threadPoolSize);
        this.schemaManager = TableSchemaManager.getInstance(consumerProps.getMappingsPath(), consumerProps.getProfilesPath());
        

        try {
            setupQueuesAndStartConsumers();
        } catch (IOException e) {
            log.write(LogStream.ERROR_MESSAGE, "Failed to initialize queues: " + e.getMessage());
            throw new RuntimeException("Failed to initialize queues", e);
        }

    }

    public int calculateThreadPoolSize() {
        long enabledQueueCount = consumerProps.getQueues().stream()
                .filter(ConsumerProperties.QueueConfig::isEnabled)
                .count();

        int threadPoolSize = enabledQueueCount > 0 ?
                (int) enabledQueueCount : Runtime.getRuntime().availableProcessors();

        if (enabledQueueCount == 0) {
            log.write(LogStream.WARNING_MESSAGE,
                    "No enabled queues found, defaulting to CPU core count: " + threadPoolSize);
        } else {
            log.write(LogStream.INFORMATION_MESSAGE,
                    "Setting thread pool size to match enabled queue count: " + threadPoolSize);
        }

        return threadPoolSize;
    }
    

    
    public void setupQueuesAndStartConsumers() throws IOException {
        Channel initChannel = null;
        try {
            initChannel = createChannel();
            
            // First, declare all dead letter queues
            for (ConsumerProperties.QueueConfig consumerQueue : consumerProps.getQueues()) {
                if (!consumerQueue.isEnabled()) {
                    continue;
                }
                
                QueueMetadataEntry queueMetadataEntry = queueMetadataLoader.getEntries().stream()
                    .filter(entry -> entry.getName().equals(consumerQueue.getName()))
                    .findFirst()
                    .orElse(null);

                if (queueMetadataEntry == null) {
                    continue;
                }

                // Declare dead letter exchange and queue first
                String dlqName = queueMetadataEntry.getName() + ".dlq";
                
                initChannel.exchangeDeclare(DLX_NAME, "direct", true);
                
                // Add quorum queue arguments
                Map<String, Object> dlqArguments = new HashMap<>();
                dlqArguments.put("x-queue-type", "quorum");
                // Use the same quorum size as the original queue if applicable
                if (queueMetadataEntry.getQuorumSize() > 0) {
                    dlqArguments.put("x-quorum-initial-group-size", queueMetadataEntry.getQuorumSize());
                }
                
                initChannel.queueDeclare(dlqName, true, false, false, dlqArguments);
                initChannel.queueBind(dlqName, DLX_NAME, queueMetadataEntry.getName());
                
                log.write(LogStream.INFORMATION_MESSAGE, 
                    "Declared dead letter queue: " + dlqName + 
                    " with exchange: " + DLX_NAME);
            }

            // Then declare normal queues with DLQ configuration
            for (ConsumerProperties.QueueConfig consumerQueue : consumerProps.getQueues()) {
                if (!consumerQueue.isEnabled()) {
                    log.write(LogStream.INFORMATION_MESSAGE,
                        "Skipping disabled queue: " + consumerQueue.getName());
                    continue;
                }
                
                QueueMetadataEntry queueMetadataEntry = queueMetadataLoader.getEntries().stream()
                    .filter(entry -> entry.getName().equals(consumerQueue.getName()))
                    .findFirst()
                    .orElse(null);

                if (queueMetadataEntry == null) {
                    log.write(LogStream.WARNING_MESSAGE,
                        "No metadata found for queue: " + consumerQueue.getName());
                    continue;
                }

                // Declare exchange
                initChannel.exchangeDeclare(
                    queueMetadataEntry.getExchange(),
                    queueMetadataEntry.getExchangeType(),
                    queueMetadataEntry.isDurable()
                );
                
                // Prepare queue arguments including DLQ configuration
                Map<String, Object> arguments = new HashMap<>();
                if ("quorum".equals(queueMetadataEntry.getType())) {
                    arguments.put("x-queue-type", "quorum");
                    if (queueMetadataEntry.getQuorumSize() > 0) {
                        arguments.put("x-quorum-initial-group-size", queueMetadataEntry.getQuorumSize());
                    }
                }
                // Add DLQ configuration
                arguments.put("x-dead-letter-exchange", DLX_NAME);
                arguments.put("x-dead-letter-routing-key", queueMetadataEntry.getName());
                
                // Declare queue with DLQ configuration
                initChannel.queueDeclare(
                    queueMetadataEntry.getName(),
                    queueMetadataEntry.isDurable(),
                    queueMetadataEntry.isExclusive(),
                    queueMetadataEntry.isAutoDelete(),
                    arguments
                );
                
                // Bind queue to exchange
                initChannel.queueBind(
                    queueMetadataEntry.getName(),
                    queueMetadataEntry.getExchange(),
                    queueMetadataEntry.getRoutingKey()
                );
                
                log.write(LogStream.INFORMATION_MESSAGE, 
                    "Declared queue: " + queueMetadataEntry.getName() + 
                    " with exchange: " + queueMetadataEntry.getExchange());

                // Create a dedicated thread for each queue
                executorService.submit(() -> {
                    log.write(LogStream.INFORMATION_MESSAGE, "Starting thread: " + Thread.currentThread().getName());

                    BatchMessageProcessor batchMessageProcessor = null;
                    LogStream queueLog = null;
                    try {
                        queueLog = new LogStream(queueMetadataEntry.getName(), logFilePath);
                        batchMessageProcessor = new BatchMessageProcessor(
                                RabbitMQConsumer.createParserInstance(consumerProps.getParserClassName()),
                                new MessageDataMapper(schemaManager, queueLog),
                                sqlSessionFactory,
                                queueLog,
                                queueMetadataEntry.getName(),
                                schemaManager,
                                connection,
                                queueMetadataEntry.getRoutingKey()
                                );
                    } catch (Exception e) {
                        queueLog.write(LogStream.ERROR_MESSAGE, "Failed to create parser instance: " + e.getMessage());
                        throw new RuntimeException("Failed to initialize BatchMessageProcessor", e);
                    }

                    
                    try {
                        batchMessageProcessor.startConsuming(queueMetadataEntry, consumerQueue);
                        queueLog.write(LogStream.INFORMATION_MESSAGE,
                            "Started consuming from queue: " + consumerQueue.getName());
                    } catch (Exception e) {
                        queueLog.write(LogStream.ERROR_MESSAGE,
                            "Failed to consume from queue: " + consumerQueue.getName() + 
                            ". " + e.getMessage());
                    }
                });
                
                log.write(LogStream.INFORMATION_MESSAGE,
                    "Initialized dedicated thread for queue: " + consumerQueue.getName());
            }
        } finally {
            if (initChannel != null && initChannel.isOpen()) {
                try {
                    initChannel.close();
                } catch (IOException | TimeoutException e) {
                    log.write(LogStream.ERROR_MESSAGE, 
                        "Error while closing initialization channel: " + e.getMessage());
                }
            }
        }
    }

    /**
     * Registers a permanent subscription to the queue.
     * The deliverCallback is called automatically by RabbitMQ whenever a new message arrives.
     * RabbitMQ will keep sending messages (up to prefetchCount) as long as:
     * - There are messages in the queue
     * - The channel is open
     * - The consumer hasn't been cancelled
     */
    // public void startConsuming(QueueMetadataEntry queueMetadata, ConsumerProperties.QueueConfig consumerQueue, BatchMessageProcessor batchMessageProcessor)
    //         throws Exception {
    //     log.write(LogStream.INFORMATION_MESSAGE, "Starting to consume from queue: " + queueMetadata.getName());
    //     Channel channel = createChannel();
    //     //log the channel info,including the channel number and the thread name
    //     log.write(LogStream.INFORMATION_MESSAGE, "Starting Channel number: " + channel.getChannelNumber() + ", Thread name: " + Thread.currentThread().getName());
    //     int prefetchCount = consumerQueue.getPrefetchCount() > 0 ? 
    //             consumerQueue.getPrefetchCount() : DEFAULT_PREFETCH_COUNT;
    //     channel.basicQos(prefetchCount);
        
    //     // Create a shared batch list and timestamp
    //     List<Delivery> messageBatch = new ArrayList<>();
    //     AtomicLong lastProcessedTime = new AtomicLong(System.currentTimeMillis());

    //     DeliverCallback deliverCallback = (consumerTag, delivery) -> {
    //         synchronized (messageBatch) {
    //             messageBatch.add(delivery);
    //             log.write(LogStream.INFORMATION_MESSAGE, queueMetadata.getName() + " Received message with delivery tag: " + delivery.getEnvelope().getDeliveryTag());
                
    //             if (messageBatch.size() >= prefetchCount) {
    //                 log.write(LogStream.INFORMATION_MESSAGE, queueMetadata.getName() + " Batch size reached, processing batch...");
    //                 // Copy the batch and clear the original list
    //                 List<Delivery> batchToProcess = new ArrayList<>(messageBatch);
    //                 messageBatch.clear();
    //                 lastProcessedTime.set(System.currentTimeMillis());
    //                 try {
    //                     // Process the batch
    //                     batchMessageProcessor.processBatchDeliveriesBulkTransaction(batchToProcess);
    //                 } catch (Exception e) {
    //                     log.write(LogStream.ERROR_MESSAGE, "Error processing batch: " + e.getMessage());
    //                     log.writeExceptionStack(e);
    //                     // If channel is closed, remove all messages from batch
    //                     messageBatch.clear();
                        
    //                     handleChannelError(channel, e);
    //                     return; // Exit callback to allow channel recovery
    //                 }
    //             }
    //         }
    //     };

    //     // Start consuming continuously
    //     channel.basicConsume(queueMetadata.getName(), false, deliverCallback,
    //             tag -> log.write(LogStream.WARNING_MESSAGE,
    //                     "Consumer cancelled by broker: " + tag)
    //     );

        
    //     while (isRunning && !Thread.currentThread().isInterrupted()) {
    //         try {
    //             TimeUnit.SECONDS.sleep(BATCH_PROCESS_INTERVAL_MS/1000);
    //             synchronized (messageBatch) {
    //                 if (!messageBatch.isEmpty()  &&  messageBatch.size() < prefetchCount &&
    //                     System.currentTimeMillis() - lastProcessedTime.get() >= BATCH_PROCESS_INTERVAL_MS) {
    //                     log.write(LogStream.INFORMATION_MESSAGE, queueMetadata.getName() + " Batch interval reached, processing batch...");
    //                     List<Delivery> batchToProcess = new ArrayList<>(messageBatch);
    //                     messageBatch.clear();
    //                     lastProcessedTime.set(System.currentTimeMillis());
    //                     try {
    //                         batchMessageProcessor.processBatchDeliveriesBulkTransaction(batchToProcess);
    //                     } catch (Exception e) {
    //                         log.write(LogStream.ERROR_MESSAGE, "Error processing batch: " + e.getMessage());
    //                         log.writeExceptionStack(e);
    //                         handleChannelError(channel, e);
    //                     }
    //                 }
    //             }
    //         } catch (InterruptedException e) {
    //             Thread.currentThread().interrupt();
    //             break;
    //         }
    //     }

    // }
    
    private Connection createConnection(String[] hosts) throws IOException, TimeoutException {
        // Create Address array from hosts
        Address[] addresses = new Address[hosts.length];
        for (int i = 0; i < hosts.length; i++) {
            addresses[i] = new Address(hosts[i], connectionFactory.getPort());
        }
        
        Connection newConnection = connectionFactory.newConnection(addresses);
        newConnection.addShutdownListener(cause -> {
            if (!cause.isInitiatedByApplication()) {
                log.write(LogStream.ERROR_MESSAGE, 
                    "Connection was closed unexpectedly: " + cause.getReason());
                handleConnectionFailure();
            }
        });
        return newConnection;
    }

    private void handleConnectionFailure() {
        int retryCount = 0;
        int maxRetries = 3;
        long retryDelay = RECOVERY_INTERVAL_MS;

        while (retryCount < maxRetries) {
            try {
                log.write(LogStream.WARNING_MESSAGE, 
                    "Attempting to reconnect (attempt " + (retryCount + 1) + " of " + maxRetries + ")");
                
                // Close existing connection if it exists
                if (connection != null && connection.isOpen()) {
                    connection.close();
                }
                
                
                // Create new connection
                connection = createConnection(mqProps.getRabbitMQHosts());
                
                // Reinitialize queues
                setupQueuesAndStartConsumers();
                
                log.write(LogStream.INFORMATION_MESSAGE, "Successfully reconnected to RabbitMQ");
                return;
                
            } catch (IOException | TimeoutException e) {
                retryCount++;
                log.write(LogStream.ERROR_MESSAGE, 
                    "Reconnection attempt failed: " + e.getMessage());
                
                if (retryCount < maxRetries) {
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return;
                    }
                }
            }
        }
        
        log.write(LogStream.ERROR_MESSAGE, 
            "Failed to reconnect after " + maxRetries + " attempts. Shutting down...");
        shutdown();
    }

    private Channel createChannel() throws IOException {
        // Thread currentThread = Thread.currentThread();
        // Channel channel = threadChannels.get(currentThread);
        Channel channel = null;
        if (channel == null || !channel.isOpen()) {
            log.write(LogStream.WARNING_MESSAGE, "Creating new channel for thread: " + Thread.currentThread().getName());
            channel = connection.createChannel();
            channel.addShutdownListener(cause -> {
                if (!cause.isInitiatedByApplication()) {
                    log.write(LogStream.ERROR_MESSAGE, 
                        "Channel was closed unexpectedly: " + cause.getReason());
                    // threadChannels.remove(currentThread);
                }
            });
            // threadChannels.put(currentThread, channel);
        }
        return channel;
    }

    // public void processBatch(List<Delivery> batchToProcess, Channel channel) {
    //     List<String> messages = new ArrayList<>();
    //     try {
    //         // Extract message content
    //         for (Delivery delivery : batchToProcess) {
    //             String message = new String(delivery.getBody(), StandardCharsets.UTF_8);
    //             messages.add(message);
    //         }
            

    //         // Use the initialized processor
    //         boolean success = batchMessageProcessor.processMessages(messages);
    //         log.write(LogStream.INFORMATION_MESSAGE, "Successfully processed batch.Will ack the batch");

    //         if (success) {
    //             // Acknowledge all messages in the batch with a single ack
    //             long lastDeliveryTag = batchToProcess.get(batchToProcess.size() - 1).getEnvelope().getDeliveryTag();
    //             channel.basicAck(lastDeliveryTag, true);
              
    //             // Print out the tag range which has been ack'd
    //             long firstDeliveryTag = batchToProcess.get(0).getEnvelope().getDeliveryTag();
    //             log.write(LogStream.INFORMATION_MESSAGE, 
    //                 String.format("Successfully ack'd batch with delivery tag range: %d - %d", 
    //                     firstDeliveryTag, lastDeliveryTag));
    //         } else {
    //             throw new RuntimeException("Failed to process message batch");
    //         }
            
    //     } catch (Exception e) {
    //         log.write(LogStream.ERROR_MESSAGE, "Error processing batch: " + e.getMessage());
    //         log.writeExceptionStack(e);
    //         try {
    //         	if(channel != null && channel.isOpen()){
    //                 for (Delivery delivery : batchToProcess) {
    //                     log.write(LogStream.ERROR_MESSAGE, 
    //                         String.format("Failed to process message with delivery tag: %d", 
    //                         delivery.getEnvelope().getDeliveryTag()));
    //                     channel.basicNack(delivery.getEnvelope().getDeliveryTag(), false, isRecoverableError(e));
    //                 }
    //             }else{
    //                 log.write(LogStream.ERROR_MESSAGE, "Channel is closed, cannot NACK messages");
    //                 throw e;
    //             }
    //         } catch (IOException ioException) {
    //             log.write(LogStream.ERROR_MESSAGE, "Error NACKing batch: " + ioException);
    //         }
    //     }
    // }



    public boolean isRecoverableError(Exception e) {
        // Implement logic to determine if an error is recoverable.
        if (e instanceof IOException || e instanceof TimeoutException) {
            return true; // Network or connection issues are usually recoverable
        }
        return false;
    }


    public void shutdown() {
        isRunning = false;
        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    @Override
    public void close() throws Exception {
        shutdown();
        
        // Close all channels first
        // for (Channel channel : threadChannels.values()) {
        //     if (channel != null && channel.isOpen()) {
        //         try {
        //             channel.close();
        //         } catch (IOException | TimeoutException e) {
        //             log.write(LogStream.ERROR_MESSAGE, "Error closing channel: " + e.getMessage());
        //         }
        //     }
        // }
        // threadChannels.clear();
        
        // Then close the connection
        if (connection != null && connection.isOpen()) {
            connection.close();
        }
        
        log.write(LogStream.INFORMATION_MESSAGE, "RabbitMQ consumer closed");
    }

    private void handleChannelError(Channel channel, Exception e) {
        log.write(LogStream.ERROR_MESSAGE, "Channel error occurred: " + e.getMessage());
        if (channel != null) {
            try {
                if (channel.isOpen()) {
                    channel.close();
                }
            } catch (IOException | TimeoutException ex) {
                log.write(LogStream.ERROR_MESSAGE, "Error closing channel: " + ex.getMessage());
            }
        }
    }

    public static MessageParser createParserInstance(String className) throws Exception {
        try {
            // Use reflection to create an instance of the specified class
            return (MessageParser) Class.forName(className).getDeclaredConstructor().newInstance();
        } catch (ClassNotFoundException | InstantiationException | IllegalAccessException e) {
            throw new Exception("Failed to create parser instance: " + e.getMessage(), e);
        }
    }
    
    public static void main(String[] args) {
        if (args.length < 2 || args.length > 3) {
            System.err.println("Usage: RabbitMQConsumer <configProfile> <LOG_FILE> [LOG_LEVEL]");
            System.exit(1);
        }

        String configProfile = args[0];
        String logfilePath = args[1];
        if (!logfilePath.endsWith(File.separator)) {
            logfilePath += File.separator;
        }
        System.out.println("log file path:" + logfilePath);
    
        int logLevel = args.length == 3 ? Integer.parseInt(args[2]) : 3;
        LogStream log = new LogStream("Consumer.log", logfilePath);
        log.setLogLevel(logLevel);

        try {
            // Load configuration
            ConfigurationLoader configLoader = new ConfigurationLoader(log, configProfile);
            configLoader.loadConfiguration();

            // Initialize MyBatis SqlSessionFactory
            String resource = configLoader.getConsumerProperties().getMybatisConfigPath();
            InputStream inputStream = new FileInputStream(resource);
            SqlSessionFactory sqlSessionFactory = new SqlSessionFactoryBuilder().build(inputStream);

            // Create connection factory with cluster support
            ConnectionFactory connectionFactory = new ConnectionFactory();
            RabbitMQProperties mqProps = configLoader.getRabbitMQProperties();
            
            // Create Address array from hosts
            Address[] addresses = new Address[mqProps.getRabbitMQHosts().length];
            for (int i = 0; i < mqProps.getRabbitMQHosts().length; i++) {
                addresses[i] = new Address(mqProps.getRabbitMQHosts()[i], mqProps.getRabbitMQPort());
            }
            
            connectionFactory.setUsername(mqProps.getRabbitMQUsername());
            connectionFactory.setPassword(mqProps.getRabbitMQPassword());
            connectionFactory.setAutomaticRecoveryEnabled(true);
            connectionFactory.setNetworkRecoveryInterval(10000); // 10 seconds
            
            // Create and use consumer with try-with-resources
            try (RabbitMQConsumer consumer = new RabbitMQConsumer(
                    mqProps,
                    configLoader.getConsumerProperties(),
                    logfilePath,
                    log,
                    connectionFactory,
                    sqlSessionFactory
                    )) {
                
                // Add shutdown hook
                Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                    log.write(LogStream.INFORMATION_MESSAGE, "Shutting down RabbitMQConsumer...");
                    try {
                        consumer.close();
                    } catch (Exception e) {
                        log.write(LogStream.ERROR_MESSAGE, "Error during shutdown: " + e.getMessage());
                    }
                }));

                // Keep the main thread alive
                while (!Thread.currentThread().isInterrupted()) {
                    Thread.sleep(1000);
                }
            } catch (Exception e) {
                log.write(LogStream.ERROR_MESSAGE, "Fatal error in consumer: " + e.getMessage());
                log.writeExceptionStack(e);
                throw e; // Re-throw to be caught by outer try-catch
            }
        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, "Application error: " + e.getMessage());
            System.exit(1);
        }
    }

    // private void declareDeadLetterQueue(Channel channel, String originalQueueName) throws IOException {
    //     // Declare the dead letter exchange
    //     String dlxName = originalQueueName + ".dlx";
    //     channel.exchangeDeclare(dlxName, "direct", true);
        
    //     // Declare the dead letter queue
    //     String dlqName = originalQueueName + ".dlq";
    //     channel.queueDeclare(dlqName, true, false, false, null);
        
    //     // Bind the DLQ to the DLX
    //     channel.queueBind(dlqName, dlxName, originalQueueName);
        
    //     // Set up the original queue with dead letter configuration
    //     Map<String, Object> arguments = new HashMap<>();
    //     arguments.put("x-dead-letter-exchange", dlxName);
    //     arguments.put("x-dead-letter-routing-key", originalQueueName);
        
    //     // Declare the original queue with DLQ configuration
    //     channel.queueDeclare(originalQueueName, true, false, false, arguments);

    //     log.write(LogStream.INFORMATION_MESSAGE, 
    //     "Successfully declared dead letter queue configuration for " + originalQueueName);
    // }
}































