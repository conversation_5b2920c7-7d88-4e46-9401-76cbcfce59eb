{"messageProfiles": [{"tabCode": "LID", "mqMessageId": "309", "msgType": "ASCII", "tableName": "LID"}, {"tabCode": "FTPRAW", "mqMessageId": "222", "msgType": "ASCII", "tableName": "FTP_RAW"}, {"tabCode": "FTPSUM", "mqMessageId": "223", "msgType": "ASCII", "tableName": "FTP_SUM"}, {"tabCode": "WASHCANDELA", "mqMessageId": "309", "msgType": "ASCII", "tableName": "WASHCANDELA"}, {"tabCode": "TDSCHANNEL", "mqMessageId": "301", "msgType": "ASCII", "tableName": "TDS_CHANNEL_STAT"}, {"tabCode": "TDSCLUSTER", "mqMessageId": "304", "msgType": "ASCII", "tableName": "TDS_CLUSTER"}, {"tabCode": "TDSCLUSDATA", "mqMessageId": "305", "msgType": "ASCII", "tableName": "TDS_CLUSTER_DATA"}, {"tabCode": "TDSENG", "mqMessageId": "310", "msgType": "ASCII", "tableName": "TDS_Eng"}, {"tabCode": "TDSGRADE", "mqMessageId": "303", "msgType": "ASCII", "tableName": "TDS_GRADE"}, {"tabCode": "TDSHEADSTAT", "mqMessageId": "302", "msgType": "ASCII", "tableName": "TDS_HEAD_STAT"}, {"tabCode": "TDSSCAN", "mqMessageId": "300", "msgType": "ASCII", "tableName": "TDS_SCAN"}, {"tabCode": "TDSSITEMARK", "mqMessageId": "306", "msgType": "ASCII", "tableName": "TDS_SITEMARK_CLUS"}, {"tabCode": "GLDRSLT", "mqMessageId": "2", "msgType": "Binary", "tableName": "GLDRSLT"}, {"tabCode": "CLIPRSLT", "mqMessageId": "3", "msgType": "Binary", "tableName": "CLIPRSLT"}, {"tabCode": "SECTRSLT", "mqMessageId": "6", "msgType": "Binary", "tableName": "SECTRSLT"}, {"tabCode": "TRCKRSLT", "mqMessageId": "7", "msgType": "Binary", "tableName": "TRCKRSLT"}, {"tabCode": "BANDRSLT", "mqMessageId": "8", "msgType": "Binary", "tableName": "BANDRSLT"}, {"tabCode": "HEADLOG", "mqMessageId": "10", "msgType": "Binary", "tableName": "HEADLOG"}, {"tabCode": "BATCHINFO", "mqMessageId": "11", "msgType": "Binary", "tableName": "BATCHINFO"}, {"tabCode": "TLOG", "mqMessageId": "12", "msgType": "Binary", "tableName": "TLOG"}, {"tabCode": "MONDATA", "mqMessageId": "15", "msgType": "Binary", "tableName": "MONDATA"}, {"tabCode": "GMONDATA", "mqMessageId": "18", "msgType": "Binary", "tableName": "GMONDATA"}, {"tabCode": "MONRSLT", "mqMessageId": "19", "msgType": "Binary", "tableName": "MONRSLT"}, {"tabCode": "GAUGERUN", "mqMessageId": "20", "msgType": "Binary", "tableName": "GAUGERUN"}, {"tabCode": "SH_GMONDATA", "mqMessageId": "21", "msgType": "Binary", "tableName": "SH_GMONDATA"}, {"tabCode": "MISCDATA", "mqMessageId": "24", "msgType": "Binary", "tableName": "MISCDATA"}, {"tabCode": "GEOMRSLT", "mqMessageId": "25", "msgType": "Binary", "tableName": "GEOMRSLT"}, {"tabCode": "DECOGAUGE", "mqMessageId": "26", "msgType": "Binary", "tableName": "DECOGAUGE"}, {"tabCode": "BANDRSLT_STRING", "mqMessageId": "27", "msgType": "Binary", "tableName": "BANDRSLT_STRING"}, {"tabCode": "GLDLOG", "mqMessageId": "29", "msgType": "Binary", "tableName": "GLDLOG"}, {"tabCode": "TESTLOG", "mqMessageId": "30", "msgType": "Binary", "tableName": "TESTLOG"}, {"tabCode": "SOFTRSLT", "mqMessageId": "31", "msgType": "Binary", "tableName": "SOFTRSLT"}, {"tabCode": "MONRSLT", "mqMessageId": "32", "msgType": "Binary", "tableName": "MONRSLT"}, {"tabCode": "GAUGERUN", "mqMessageId": "33", "msgType": "Binary", "tableName": "GAUGERUN"}, {"tabCode": "GLDLOG", "mqMessageId": "34", "msgType": "Binary", "tableName": "GLDLOG"}, {"tabCode": "TESTLOG", "mqMessageId": "35", "msgType": "Binary", "tableName": "TESTLOG"}, {"tabCode": "TRCKRSLT", "mqMessageId": "36", "msgType": "Binary", "tableName": "TRCKRSLT"}, {"tabCode": "GLDPARM", "mqMessageId": "37", "msgType": "Binary", "tableName": "GLDPARM"}, {"tabCode": "MAGCBPRM", "mqMessageId": "38", "msgType": "Binary", "tableName": "MAGCBPRM"}, {"tabCode": "MAGMBPRM", "mqMessageId": "39", "msgType": "Binary", "tableName": "MAGMBPRM"}, {"tabCode": "SCNRINFO", "mqMessageId": "40", "msgType": "Binary", "tableName": "SCNRINFO"}, {"tabCode": "GLDLOG", "mqMessageId": "41", "msgType": "Binary", "tableName": "GLDLOG"}, {"tabCode": "TESTLOG", "mqMessageId": "42", "msgType": "Binary", "tableName": "TESTLOG"}, {"tabCode": "HEADLOG", "mqMessageId": "43", "msgType": "Binary", "tableName": "HEADLOG"}, {"tabCode": "GMONDATA", "mqMessageId": "44", "msgType": "Binary", "tableName": "GMONDATA"}, {"tabCode": "SH_GMONDATA", "mqMessageId": "45", "msgType": "Binary", "tableName": "SH_GMONDATA"}]}