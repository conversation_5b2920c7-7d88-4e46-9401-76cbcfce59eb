package wdc.disk.ecs.apps.MQUpload.schema;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.Map;
import java.io.File;
import java.lang.reflect.Method;
import java.lang.reflect.InvocationTargetException;


public class TableSchemaManager {
    private final String profileBasePath;
    private static TableSchemaManager instance;
    private final String basePackage = "wdc.disk.ecs.apps.MQUpload";
    
    private Map<String, TableConfigParser.TableDefinition> tableDefinitionsMap;
    
    private TableSchemaManager(String messageMappingsPath, String profileBasePath) {
        this.profileBasePath = profileBasePath;
        try {
            // Load table definitions during initialization
            this.tableDefinitionsMap = TableConfigParser.loadMsgMappingFromJsonProfile(messageMappingsPath);
        } catch (IOException e) {
            throw new SchemaException("Failed to load table definitions", e);
        }
    }

    public static synchronized TableSchemaManager getInstance(String messageMappingsPath, String profileBasePath) {
        if (instance == null) {
            instance = new TableSchemaManager(messageMappingsPath, profileBasePath);
        }
        return instance;
    }

    /**
     * Gets table definition and lazily loads its columns if needed
     */
    public TableConfigParser.TableDefinition getTableDefinitionFromTabCode(String tabCode) throws SchemaException {
        TableConfigParser.TableDefinition tableDef = tableDefinitionsMap.get(tabCode);
        if (tableDef == null) {
            throw new SchemaException("No schema found for tabCode: " + tabCode);
        }
        
        // Lazy load columns if they haven't been loaded yet
        if (tableDef.getColumns().isEmpty()) {
            synchronized (tableDef) {
                if (tableDef.getColumns().isEmpty()) {
                    try {
                        loadColumnDefinitions(tableDef);
                    } catch (IOException e) {
                        throw new SchemaException("Failed to load column definitions for tabCode: " + tabCode, e);
                    }
                }
            }
        }
        
        return tableDef;
    }

    private void loadColumnDefinitions(TableConfigParser.TableDefinition tableDef) throws IOException {
        // Construct absolute path
        File profileFile = new File(profileBasePath);
        String absoluteProfilePath = String.format("%s%s%s_%s.pro", 
            profileFile.getAbsolutePath().endsWith(File.separator) ? 
                profileFile.getAbsolutePath().substring(0, profileFile.getAbsolutePath().length() - 1) : 
                profileFile.getAbsolutePath(),
            File.separator,
            tableDef.getMqMessageId(), 
            tableDef.getTabCode());
        
        File profileFileToRead = new File(absoluteProfilePath);
        if (!profileFileToRead.exists()) {
            throw new IOException("Profile file does not exist: " + absoluteProfilePath);
        }
            
        try (BufferedReader reader = new BufferedReader(new FileReader(absoluteProfilePath))) {
            String line;
            boolean headerFound = false;
            
            // Skip until header line
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("*") && line.contains("ColumnName")) {
                    headerFound = true;
                    break;
                }
            }
            
            if (!headerFound) {
                throw new IOException("Invalid profile format: header not found in " + absoluteProfilePath);
            }
            
            // Parse column definitions
            while ((line = reader.readLine()) != null) {
                if (line.trim().isEmpty()) break;
                
                TableConfigParser.ColumnDefinition column = TableConfigParser.parseColumnRow(line);
                if (column != null) {
                    tableDef.addColumn(column);
                }
            }
        }
    }

    /**
     * Gets the appropriate MyBatis mapper for a given tabCode
     */
    public Class<?> getMapperClassForTabCode(String tabCode) throws ClassNotFoundException {
        TableConfigParser.TableDefinition tableDef = getTableDefinitionFromTabCode(tabCode);
        if (tableDef == null) {
            throw new SchemaException("No table definition found for tabCode: " + tabCode);
        }
        
        String mapperClassName = String.format("%s.mapper.generatedMappers.%s_Mapper", 
            basePackage, tableDef.getTabCode());
        return Class.forName(mapperClassName);
    }

    /**
     * Gets the appropriate POJO class for a given tabCode
     */
    public Class<?> getPojoClassForTabCode(String tabCode) throws ClassNotFoundException {
        TableConfigParser.TableDefinition tableDef = getTableDefinitionFromTabCode(tabCode);
        if (tableDef == null) {
            throw new SchemaException("No table definition found for tabCode: " + tabCode);
        }
        
        String pojoClassName = String.format("%s.model.generatedModels.%s", 
            basePackage, tableDef.getTabCode());
        return Class.forName(pojoClassName);
    }

    /**
     * Invokes the appropriate mapper method for the given mapper and POJO instance
     */
    public void invokeMapperMethod(Object mapper, Object pojoInstance) {
        try {
            // Try to find and invoke the insertOrUpdate method
            Method insertOrUpdate = mapper.getClass().getMethod("insertOrUpdate", pojoInstance.getClass());
            insertOrUpdate.invoke(mapper, pojoInstance);
        } catch (NoSuchMethodException e) {
            throw new SchemaException("insertOrUpdate method not found in mapper. " +
                "Expected signature: insertOrUpdate(" + pojoInstance.getClass().getName() + ")", e);
        } catch (IllegalAccessException e) {
            throw new SchemaException("Cannot access insertOrUpdate method", e);
        } catch (InvocationTargetException e) {
            throw new SchemaException("Error during mapper method execution: " + 
                (e.getCause() != null ? e.getCause().getMessage() : e.getMessage()), e);
        } catch (Exception e) {
            throw new SchemaException("Failed to invoke mapper method: " + e.getMessage(), e);
        }
    }

    /**
     * Gets tabCode based on message ID
     * @param messageId The MQ message ID to look up
     * @return The tabCode corresponding to the message ID, or null if not found
     */
    public String getTabCodeFromMessageId(String messageId) {
        for (Map.Entry<String, TableConfigParser.TableDefinition> entry : tableDefinitionsMap.entrySet()) {
            if (messageId.equals(entry.getValue().getMqMessageId())) {
                return entry.getKey();
            }
        }
        return null;
    }
}

/**
 * Custom exception for schema-related errors
 */
class SchemaException extends RuntimeException {
    public SchemaException(String message) {
        super(message);
    }
    
    public SchemaException(String message, Throwable cause) {
        super(message, cause);
    }
}






















