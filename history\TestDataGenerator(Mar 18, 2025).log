~0 15:26:53 Mar 18, 2025 Exception ibm.disk.cfgreader.ReadEcsIniException occurred, message = Unable to open file MQ_Upload.ini
ibm.disk.cfgreader.ReadEcsIniException: Unable to open file MQ_Upload.ini
	at ibm.disk.cfgreader.ConfigReader.readfile(ConfigReader.java:302)
	at ibm.disk.cfgreader.ConfigReader.<init>(ConfigReader.java:97)
	at wdc.disk.ecs.apps.SFTPSend.util.ConfigurationLoader.loadConfiguration(ConfigurationLoader.java:32)
	at wdc.disk.ecs.apps.SFTPSend.util.ConfigurationLoader.loadConfiguration(ConfigurationLoader.java:26)
	at wdc.disk.ecs.apps.MQUpload.MQUpload.initialize(MQUpload.java:59)
	at wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator.start(TestDataGenerator.java:37)
	at wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator.main(TestDataGenerator.java:73)
~0 15:26:53 Mar 18, 2025 Exception wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException occurred, message = Could not read profile MQ_Upload.ini
wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException: Could not read profile MQ_Upload.ini
	at wdc.disk.ecs.apps.SFTPSend.util.ConfigurationLoader.loadConfiguration(ConfigurationLoader.java:47)
	at wdc.disk.ecs.apps.SFTPSend.util.ConfigurationLoader.loadConfiguration(ConfigurationLoader.java:26)
	at wdc.disk.ecs.apps.MQUpload.MQUpload.initialize(MQUpload.java:59)
	at wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator.start(TestDataGenerator.java:37)
	at wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator.main(TestDataGenerator.java:73)
~0 15:30:20 Mar 18, 2025 Exception ibm.disk.cfgreader.ReadEcsIniException occurred, message = Unable to open file MQ_Upload.ini
ibm.disk.cfgreader.ReadEcsIniException: Unable to open file MQ_Upload.ini
	at ibm.disk.cfgreader.ConfigReader.readfile(ConfigReader.java:302)
	at ibm.disk.cfgreader.ConfigReader.<init>(ConfigReader.java:97)
	at wdc.disk.ecs.apps.SFTPSend.util.ConfigurationLoader.loadConfiguration(ConfigurationLoader.java:32)
	at wdc.disk.ecs.apps.SFTPSend.util.ConfigurationLoader.loadConfiguration(ConfigurationLoader.java:26)
	at wdc.disk.ecs.apps.MQUpload.MQUpload.initialize(MQUpload.java:59)
	at wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator.start(TestDataGenerator.java:37)
	at wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator.main(TestDataGenerator.java:73)
~0 15:30:20 Mar 18, 2025 Exception wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException occurred, message = Could not read profile MQ_Upload.ini
wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException: Could not read profile MQ_Upload.ini
	at wdc.disk.ecs.apps.SFTPSend.util.ConfigurationLoader.loadConfiguration(ConfigurationLoader.java:47)
	at wdc.disk.ecs.apps.SFTPSend.util.ConfigurationLoader.loadConfiguration(ConfigurationLoader.java:26)
	at wdc.disk.ecs.apps.MQUpload.MQUpload.initialize(MQUpload.java:59)
	at wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator.start(TestDataGenerator.java:37)
	at wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator.main(TestDataGenerator.java:73)
~3 15:38:41 Mar 18, 2025 Created directory: C:\opt\ecs\storage\mq\pds\\sending
~3 15:38:42 Mar 18, 2025 Created directory: C:\opt\ecs\storage\mq\pds\\sent
~3 15:38:42 Mar 18, 2025 Created directory: C:\opt\ecs\storage\mq\pds\\problem
~3 15:38:47 Mar 18, 2025 Successfully connected to RabbitMQ
~3 15:41:35 Mar 18, 2025 Successfully connected to RabbitMQ
~3 15:57:43 Mar 18, 2025 Successfully connected to RabbitMQ
~3 15:58:08 Mar 18, 2025 Initialized DB Message ID Profile: C:\opt\ecs\profiles\pdsMsgId.pro
~3 15:58:50 Mar 18, 2025 MQUpload started. Monitoring directory: C:\opt\ecs\storage\mq\pds\
~3 15:58:53 Mar 18, 2025 No files found in monitor directory.
~3 15:58:53 Mar 18, 2025 Generated test file: test_2025-03-18-15.58.53.000855.txt
~3 15:58:58 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.58.53.000855.txt
~3 15:58:58 Mar 18, 2025 Generated test file: test_2025-03-18-15.58.58.000858.txt
~3 15:59:03 Mar 18, 2025 Processing 1 files
~3 15:59:03 Mar 18, 2025 Generated test file: test_2025-03-18-15.59.03.000860.txt
~3 15:59:08 Mar 18, 2025 Generated test file: test_2025-03-18-15.59.08.000866.txt
~3 15:59:13 Mar 18, 2025 Generated test file: test_2025-03-18-15.59.13.000870.txt
~3 15:59:18 Mar 18, 2025 Generated test file: test_2025-03-18-15.59.18.000858.txt
~3 15:59:23 Mar 18, 2025 Generated test file: test_2025-03-18-15.59.23.000864.txt
~3 15:59:28 Mar 18, 2025 Generated test file: test_2025-03-18-15.59.28.000858.txt
~3 15:59:33 Mar 18, 2025 Generated test file: test_2025-03-18-15.59.33.000863.txt
~3 15:59:38 Mar 18, 2025 Generated test file: test_2025-03-18-15.59.38.000869.txt
~3 15:59:43 Mar 18, 2025 Generated test file: test_2025-03-18-15.59.43.000863.txt
~3 15:59:48 Mar 18, 2025 Generated test file: test_2025-03-18-15.59.48.000870.txt
~3 15:59:53 Mar 18, 2025 Generated test file: test_2025-03-18-15.59.53.000872.txt
~3 15:59:58 Mar 18, 2025 Generated test file: test_2025-03-18-15.59.58.000870.txt
~3 16:00:03 Mar 18, 2025 Generated test file: test_2025-03-18-16.00.03.000867.txt
~3 16:00:08 Mar 18, 2025 Generated test file: test_2025-03-18-16.00.08.000859.txt
~3 16:00:13 Mar 18, 2025 Generated test file: test_2025-03-18-16.00.13.000872.txt
~3 16:00:18 Mar 18, 2025 Generated test file: test_2025-03-18-16.00.18.000872.txt
~3 16:00:23 Mar 18, 2025 Generated test file: test_2025-03-18-16.00.23.000859.txt
~3 16:00:28 Mar 18, 2025 Generated test file: test_2025-03-18-16.00.28.000862.txt
~3 16:00:33 Mar 18, 2025 Generated test file: test_2025-03-18-16.00.33.000862.txt
~3 16:00:38 Mar 18, 2025 Generated test file: test_2025-03-18-16.00.38.000865.txt
~3 16:00:43 Mar 18, 2025 Generated test file: test_2025-03-18-16.00.43.000867.txt
~3 16:00:48 Mar 18, 2025 Generated test file: test_2025-03-18-16.00.48.000860.txt
~3 16:00:53 Mar 18, 2025 Generated test file: test_2025-03-18-16.00.53.000865.txt
~3 16:00:58 Mar 18, 2025 Generated test file: test_2025-03-18-16.00.58.000868.txt
~3 16:01:03 Mar 18, 2025 Generated test file: test_2025-03-18-16.01.03.000864.txt
~3 16:01:08 Mar 18, 2025 Generated test file: test_2025-03-18-16.01.08.000864.txt
~3 16:01:13 Mar 18, 2025 Generated test file: test_2025-03-18-16.01.13.000862.txt
~3 16:01:18 Mar 18, 2025 Generated test file: test_2025-03-18-16.01.18.000876.txt
~3 16:01:23 Mar 18, 2025 Generated test file: test_2025-03-18-16.01.23.000870.txt
~3 16:01:28 Mar 18, 2025 Generated test file: test_2025-03-18-16.01.28.000866.txt
~3 16:01:33 Mar 18, 2025 Generated test file: test_2025-03-18-16.01.33.000866.txt
~3 16:01:38 Mar 18, 2025 Generated test file: test_2025-03-18-16.01.38.000875.txt
~3 16:01:43 Mar 18, 2025 Generated test file: test_2025-03-18-16.01.43.000871.txt
~3 16:01:48 Mar 18, 2025 Generated test file: test_2025-03-18-16.01.48.000864.txt
~3 16:01:53 Mar 18, 2025 Generated test file: test_2025-03-18-16.01.53.000869.txt
~3 16:01:58 Mar 18, 2025 Generated test file: test_2025-03-18-16.01.58.000864.txt
~3 16:02:03 Mar 18, 2025 Generated test file: test_2025-03-18-16.02.03.000866.txt
~3 16:02:08 Mar 18, 2025 Generated test file: test_2025-03-18-16.02.08.000875.txt
~3 16:02:13 Mar 18, 2025 Generated test file: test_2025-03-18-16.02.13.000869.txt
~3 16:10:15 Mar 18, 2025 Successfully connected to RabbitMQ
~3 16:10:19 Mar 18, 2025 Initialized DB Message ID Profile: C:\opt\ecs\profiles\pdsMsgId.pro
~3 16:10:19 Mar 18, 2025 MQUpload started. Monitoring directory: C:\opt\ecs\storage\mq\pds\
~3 16:10:19 Mar 18, 2025 Generated test file: test_2025-03-18-16.10.19.000716.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.58.58.000858.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.59.03.000860.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.59.08.000866.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.59.13.000870.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.59.18.000858.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.59.23.000864.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.59.28.000858.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.59.33.000863.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.59.38.000869.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.59.43.000863.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.59.48.000870.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.59.53.000872.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-15.59.58.000870.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.00.03.000867.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.00.08.000859.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.00.13.000872.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.00.18.000872.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.00.23.000859.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.00.28.000862.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.00.33.000862.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.00.38.000865.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.00.43.000867.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.00.48.000860.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.00.53.000865.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.00.58.000868.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.01.03.000864.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.01.08.000864.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.01.13.000862.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.01.18.000876.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.01.23.000870.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.01.28.000866.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.01.33.000866.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.01.38.000875.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.01.43.000871.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.01.48.000864.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.01.53.000869.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.01.58.000864.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.02.03.000866.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.02.08.000875.txt
~3 16:10:19 Mar 18, 2025 Moved file to sending directory: test_2025-03-18-16.02.13.000869.txt
~3 16:10:24 Mar 18, 2025 Generated test file: test_2025-03-18-16.10.24.000728.txt
~3 16:10:24 Mar 18, 2025 Processing 41 files
~3 16:10:29 Mar 18, 2025 Generated test file: test_2025-03-18-16.10.29.000728.txt
~3 16:10:34 Mar 18, 2025 Generated test file: test_2025-03-18-16.10.34.000726.txt
~3 16:10:39 Mar 18, 2025 Generated test file: test_2025-03-18-16.10.39.000718.txt
~3 16:10:44 Mar 18, 2025 Generated test file: test_2025-03-18-16.10.44.000718.txt
~0 16:10:48 Mar 18, 2025 Profile Exception Line 168: ibm.disk.profiles.ProfileException: Invalid field: splitMode=
~3 16:10:49 Mar 18, 2025 Generated test file: test_2025-03-18-16.10.49.000726.txt
~3 16:10:54 Mar 18, 2025 Generated test file: test_2025-03-18-16.10.54.000714.txt
~3 16:10:59 Mar 18, 2025 Generated test file: test_2025-03-18-16.10.59.000714.txt
~3 16:11:04 Mar 18, 2025 Generated test file: test_2025-03-18-16.11.04.000717.txt
~3 16:11:09 Mar 18, 2025 Generated test file: test_2025-03-18-16.11.09.000722.txt
~3 16:11:14 Mar 18, 2025 Generated test file: test_2025-03-18-16.11.14.000723.txt
~3 16:11:19 Mar 18, 2025 Generated test file: test_2025-03-18-16.11.19.000718.txt
~3 16:11:24 Mar 18, 2025 Generated test file: test_2025-03-18-16.11.24.000725.txt
~3 16:11:29 Mar 18, 2025 Generated test file: test_2025-03-18-16.11.29.000723.txt
~3 16:11:34 Mar 18, 2025 Generated test file: test_2025-03-18-16.11.34.000710.txt
~3 16:11:39 Mar 18, 2025 Generated test file: test_2025-03-18-16.11.39.000722.txt
~3 16:11:44 Mar 18, 2025 Generated test file: test_2025-03-18-16.11.44.000722.txt
~3 16:11:49 Mar 18, 2025 Generated test file: test_2025-03-18-16.11.49.000711.txt
~3 16:11:54 Mar 18, 2025 Generated test file: test_2025-03-18-16.11.54.000716.txt
~3 16:11:59 Mar 18, 2025 Generated test file: test_2025-03-18-16.11.59.000719.txt
~3 16:12:04 Mar 18, 2025 Generated test file: test_2025-03-18-16.12.04.000711.txt
