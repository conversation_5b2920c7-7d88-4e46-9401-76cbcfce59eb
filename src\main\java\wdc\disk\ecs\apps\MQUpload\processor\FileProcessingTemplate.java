package wdc.disk.ecs.apps.MQUpload.processor;

import java.io.File;
import java.util.List;

import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMessage;
import wdc.disk.ecs.apps.MQUpload.service.FileOperationService;
import wdc.disk.ecs.apps.MQUpload.service.RabbitMQProducerService;

public class FileProcessingTemplate {
    protected final LogStream log;
    protected final FileProcessor processor;
    private final RabbitMQProducerService rabbitMQService;
    private final FileOperationService fileOperationService;
        
        
    public FileProcessingTemplate(LogStream log, FileProcessor processor, RabbitMQProducerService rabbitMQService, FileOperationService fileOperationService) {
        this.log = log;
        this.processor = processor;
        this.rabbitMQService = rabbitMQService;
        this.fileOperationService = fileOperationService;
    }
    

    public void processAndUploadFiles() throws Exception {
        // Check if sending directory has less than configured maximum files
        if (!fileOperationService.validateSendingDirDestination()) {
            log.write(LogStream.WARNING_MESSAGE, "Sending directory validation failed, skipping moving process");
        }else {
        	// Move files from base directory to sending directory
        	fileOperationService.moveFilesToSendingDirectory();        	
        }
        

        // Wait for any ongoing file write operations to complete
        // This ensures we don't process partially written files
        fileOperationService.waitForFileWriting();
        
        
        File[] files = fileOperationService.getSortedFilesFromSendingDir();
        if (files == null || files.length == 0) {
            log.write(LogStream.INFORMATION_MESSAGE,"No files found in monitor directory.");
            return;
        }
        log.write(LogStream.INFORMATION_MESSAGE, String.format("Processing %d files", files.length));

        for (File file : files) {
            if (file.isDirectory()) continue;
            
            try {
                if (!FileOperationService.isValidFile(file)) {
                    fileOperationService.handleInvalidFile(file);
                    continue;
                }

                List<RabbitMessage> messages = processor.processFile(file);
                rabbitMQService.uploadMessage(messages);
                log.write(LogStream.INFORMATION_MESSAGE, "Successfully uploaded file: " + file.getName());
                fileOperationService.moveToSent(file);
            } catch (Exception e) {
                log.write(LogStream.ERROR_MESSAGE, 
                    "Error processing file: " + file.getName() + " - " + e.getMessage());
                fileOperationService.moveToProblem(file);
            }
        }

        return ;
    }

    
}