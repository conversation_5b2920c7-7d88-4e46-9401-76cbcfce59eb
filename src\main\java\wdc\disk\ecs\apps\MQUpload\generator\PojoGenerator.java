package wdc.disk.ecs.apps.MQUpload.generator;

import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

import wdc.disk.ecs.apps.MQUpload.schema.TableConfigParser;
import wdc.disk.ecs.apps.MQUpload.schema.TableSchemaManager;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

public class PojoGenerator {
    private final VelocityEngine ve;
    private final String basePackage;
    private final String messageMappingsPath;

    public PojoGenerator(String basePackage, String messageMappingsPath) {
        this.basePackage = basePackage;
        this.messageMappingsPath = messageMappingsPath;
        this.ve = new VelocityEngine();
        Properties props = new Properties();
        props.setProperty("resource.loaders", "classpath");
        props.setProperty("resource.loader.classpath.class", ClasspathResourceLoader.class.getName());
        ve.init(props);
    }

    public void generateFromProfile(String profilePath, String outputDir) throws IOException {
        List<TableConfigParser.ColumnDefinition> columns = TableConfigParser.parseProfileFile(profilePath);
        String rawClassName = extractClassNameFromProfile(profilePath);
        String className = normalizeClassName(rawClassName);
        
        // Generate POJO
        generatePojo(columns, className, outputDir);
        
        // Generate Mapper Interface with columns
        generateMapperInterface(className, outputDir, columns);
    }

    private String normalizeClassName(String rawClassName) {
        return rawClassName.replaceAll("^\\d+", "");
    }

    

    private void generatePojo(List<TableConfigParser.ColumnDefinition> columns, String className, String outputDir) {
        try {
            Template template = ve.getTemplate("templates/Pojo.vm");
            VelocityContext context = new VelocityContext();
            
            context.put("package", basePackage + ".model.generatedModels");
            context.put("className", className);
            context.put("columns", columns);
            
            File dir = new File(outputDir + "/model/generatedModels/");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            String outputPath = outputDir + "/model/generatedModels/" + className + ".java";
            try (Writer writer = new FileWriter(outputPath)) {
                template.merge(context, writer);
            }
            System.out.println("Generated POJO: " + outputPath);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate POJO", e);
        }
    }

    private void generateMapperInterface(String className, String outputDir, List<TableConfigParser.ColumnDefinition> columns) {
        try {
            // Initialize TableSchemaManager with the correct paths
            TableSchemaManager schemaManager = TableSchemaManager.getInstance(
                messageMappingsPath,  // message_mappings.json path
                "profiles\\generatedProfiles"  // profile base path
            );
            
            // Get TableDefinition using tabCode (className)
            // This will trigger lazy loading of columns
            TableConfigParser.TableDefinition tableDef = schemaManager.getTableDefinitionFromTabCode(className);
            
            if (tableDef == null) {
                throw new RuntimeException("No table definition found for " + className);
            }
            
            Template template = ve.getTemplate("templates/Mapper.vm");
            VelocityContext context = new VelocityContext();
            
            context.put("package", basePackage + ".mapper.generatedMappers");
            context.put("className", className);
            context.put("modelPackage", basePackage + ".model.generatedModels");
            context.put("tableName", tableDef.getTableName());
            
            // Get unique columns (for ON clause)
            List<TableConfigParser.ColumnDefinition> uniqueColumns = tableDef.getColumns().stream()
                .filter(TableConfigParser.ColumnDefinition::isUniqueIndex)
                .collect(Collectors.toList());
            
            // Get non-unique columns (for UPDATE SET clause)
            List<TableConfigParser.ColumnDefinition> updateColumns = tableDef.getColumns().stream()
                .filter(col -> !col.isUniqueIndex())
                .collect(Collectors.toList());
            
            context.put("uniqueColumns", uniqueColumns);
            context.put("updateColumns", updateColumns);
            context.put("allColumns", tableDef.getColumns());
            
            File dir = new File(outputDir + "/mapper/generatedMappers/");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            String outputPath = outputDir + "/mapper/generatedMappers/" + className + "_Mapper.java";
            try (Writer writer = new FileWriter(outputPath)) {
                template.merge(context, writer);
            }
            System.out.println("Generated Mapper: " + outputPath);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate Mapper Interface", e);
        }
    }

    private String extractClassNameFromProfile(String profilePath) {
        File file = new File(profilePath);
        String fileName = file.getName();
        int firstUnderscoreIndex = fileName.indexOf('_');
        return fileName.substring(firstUnderscoreIndex + 1, fileName.lastIndexOf('.'));
    }

   

    public static void main(String[] args) {
        //"profiles/222_FTPRAW.pro"
        String messageMappingsPath = "src/test/resources/mapper/message_mappings.json";
        List<String> profilePaths = Arrays.asList(
        "profiles/309_LID.pro",
            "profiles/222_FTPRAW.pro",
            "profiles/223_FTPSUM.pro"
        );

        for(String profilePath : profilePaths) {
            try {
                PojoGenerator generator = new PojoGenerator(
                    "wdc.disk.ecs.apps.MQUpload",
                    messageMappingsPath
                );
                generator.generateFromProfile(profilePath, "src/main/java/wdc/disk/ecs/apps/MQUpload");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        System.out.println("Generation complete.");
    }
}








