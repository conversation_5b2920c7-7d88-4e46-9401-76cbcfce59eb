package wdc.disk.ecs.apps.MQUpload.parser;

import java.util.ArrayList;
import java.util.List;

import ibm.disk.profiles.DiskProfile;
import ibm.disk.profiles.ProfileException;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.SFTPSend.binary.BinaryService;
import wdc.disk.ecs.apps.SFTPSend.binary.SingleMessage;
import wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord;

/**
 * Binary Message Parser Implementation
 * Parses binary format messages using BinaryService logic
 */
public class BinaryMessageParser implements MessageParser {
    

}
