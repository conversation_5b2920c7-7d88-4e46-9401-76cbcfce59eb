MQUpload_test {
    // File monitoring configurations
    PDS_DIR=C:\opt\ecs\storage\mq\,  // Directory to monitor for incoming files
    BINARY_FORMAT=false,              // Set to true for binary files, false for ASCII files
    
    // File processor configurations
    MAX_FILES_IN_SENDING=10,          // Maximum number of files being processed simultaneously
    WAITING_FOR_WRITE_ENDED=100,     // Time to wait for file write completion (milliseconds)
    PACK_SAME_MSG_ID=false,           // Whether to pack files with same message ID
    TO_DIR=C:\opt\ecs\storage\mq\sent\,     // Directory for processed files
    
    // DB Message ID Profile configurations (required for ASCII format)
    DB_MSG_ID_PROFILE=C:\Development\TFS_SourceCode\ECS\SZ_GO\Common\Dev\SFTPSend_Hao\profiles\pdsMsgId.pro,  // Profile for DB Message IDs
    QUEUE_CONFIG_PATH=C:\Development\TFS_SourceCode\ECS\SZ_GO\Common\Dev\SFTPSend_Hao\src\test\resources\test-queue-config.csv,
}

RabbitMQ_Configurations {
    MONITOR_INTERVAL=10,              // Monitoring interval in seconds
    RABBITMQ_HOST=*************,          // RabbitMQ server hostname
    RABBITMQ_PORT=5672,              // RabbitMQ AMQP port
    RABBITMQ_USERNAME=guest,          // RabbitMQ username
    RABBITMQ_PASSWORD=guest,          // RabbitMQ password
    RABBITMQ_EXCHANGE=ecs.direct,  // Exchange name for file uploads
}

Consumer_test {
	QUEUES = 309/318, 	
}