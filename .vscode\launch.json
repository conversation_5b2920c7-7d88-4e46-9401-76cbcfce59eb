{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "DBRecordCountComparator",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.dbcompare.DBRecordCountComparator",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "ProfileAndMapperGenerator",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.MQUpload.generator.ProfileAndMapperGenerator",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "RabbitMQConsumer",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.MQUpload.consumer.RabbitMQConsumer",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "MapperGenerator",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.MQUpload.generator.MapperGenerator",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "MsgProfileGenerator",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.MQUpload.generator.MsgProfileGenerator",
            "projectName": "SFTPSend",
            "args": [
                "DB2SYS",
                "WASHCANDELA",
                "C:\\Users\\<USER>\\Desktop\\",
                "false"
            ]
        },
        {
            "type": "java",
            "name": "PojoGenerator",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.MQUpload.generator.PojoGenerator",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "MQUpload",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.MQUpload.producer.MQUpload",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "TableConfigParser",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.MQUpload.schema.TableConfigParser",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "MessageGenerator",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.MQUpload.util.MessageGenerator",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "TestDataGenerator",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.MQUpload.util.TestDataGenerator",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "MonitorTalend",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.SFTPSend.MonitorTalend",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "SFTPSendApp",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.SFTPSend.SFTPSendApp",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "SFTPSendView",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.SFTPSend.SFTPSendView",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "ConvertorUtility",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.SFTPSend.common.pds.conversion.ConvertorUtility",
            "projectName": "SFTPSend"
        },
        {
            "type": "java",
            "name": "TestSmb",
            "request": "launch",
            "mainClass": "wdc.disk.ecs.apps.SFTPSend.thread.TestSmb",
            "projectName": "SFTPSend"
        }
    ]
}