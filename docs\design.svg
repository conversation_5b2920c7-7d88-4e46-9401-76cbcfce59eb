<svg viewBox="0 0 1200 1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="serverGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="rabbitGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E55528;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dbGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2ECC71;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#27AE60;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <!-- Arrow marker definition moved here -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495E"/>
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="220" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2C3E50">
    RabbitMQ System Architecture
  </text>
  
  <!-- ECS Workstations Layer -->
  <text x="50" y="80" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2C3E50">
    ECS Workstations (200+ Stations)
  </text>
  
  <!-- Sample ECS Workstations with integrated Producer -->
  <g id="ecs-stations">
    <!-- Station 1 -->
    <rect x="50" y="100" width="80" height="80" rx="5" fill="url(#serverGradient)" filter="url(#shadow)"/>
    <text x="90" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white" font-weight="bold">ECS Station 1</text>
    <rect x="55" y="130" width="70" height="25" rx="3" fill="#9B59B6" opacity="0.8"/>
    <text x="90" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Producer</text>
    <rect x="55" y="160" width="70" height="15" rx="2" fill="#F39C12"/>
    <text x="90" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">Data Files</text>
    
    <!-- Station 2 -->
    <rect x="150" y="100" width="80" height="80" rx="5" fill="url(#serverGradient)" filter="url(#shadow)"/>
    <text x="190" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white" font-weight="bold">ECS Station 2</text>
    <rect x="155" y="130" width="70" height="25" rx="3" fill="#9B59B6" opacity="0.8"/>
    <text x="190" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Producer</text>
    <rect x="155" y="160" width="70" height="15" rx="2" fill="#F39C12"/>
    <text x="190" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">Data Files</text>
    
    <!-- Station 3 -->
    <rect x="250" y="100" width="80" height="80" rx="5" fill="url(#serverGradient)" filter="url(#shadow)"/>
    <text x="290" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white" font-weight="bold">ECS Station 3</text>
    <rect x="255" y="130" width="70" height="25" rx="3" fill="#9B59B6" opacity="0.8"/>
    <text x="290" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Producer</text>
    <rect x="255" y="160" width="70" height="15" rx="2" fill="#F39C12"/>
    <text x="290" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">Data Files</text>
    
    <!-- Dots indicating more stations -->
    <circle cx="370" cy="140" r="3" fill="#2C3E50"/>
    <circle cx="385" cy="140" r="3" fill="#2C3E50"/>
    <circle cx="400" cy="140" r="3" fill="#2C3E50"/>
    
    <!-- Station N -->
    <rect x="450" y="100" width="80" height="80" rx="5" fill="url(#serverGradient)" filter="url(#shadow)"/>
    <text x="490" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white" font-weight="bold">ECS Station N</text>
    <rect x="455" y="130" width="70" height="25" rx="3" fill="#9B59B6" opacity="0.8"/>
    <text x="490" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Producer</text>
    <rect x="455" y="160" width="70" height="15" rx="2" fill="#F39C12"/>
    <text x="490" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">Data Files</text>
  </g>
  
  <!-- RabbitMQ Broker -->
  <text x="50" y="220" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2C3E50">
    RabbitMQ Broker
  </text>
  
  <rect x="50" y="260" width="700" height="180" rx="10" fill="#FFF8E1" stroke="#FF6B35" stroke-width="3" filter="url(#shadow)"/>
  
  <!-- Exchange -->
  <rect x="300" y="280" width="120" height="40" rx="8" fill="#E74C3C" stroke="#C0392B" stroke-width="2"/>
  <text x="360" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white" font-weight="bold">Exchange</text>
  
  <!-- Queues -->
  <text x="70" y="350" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2C3E50">
    Queues (50+ Message Types)
  </text>
  
  <g id="queues">
    <rect x="70" y="360" width="80" height="30" rx="4" fill="#3498DB" stroke="#2980B9" stroke-width="1"/>
    <text x="110" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Queue 1</text>
    
    <rect x="160" y="360" width="80" height="30" rx="4" fill="#3498DB" stroke="#2980B9" stroke-width="1"/>
    <text x="200" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Queue 2</text>
    
    <rect x="250" y="360" width="80" height="30" rx="4" fill="#3498DB" stroke="#2980B9" stroke-width="1"/>
    <text x="290" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Queue 3</text>
    
    <circle cx="350" cy="375" r="2" fill="#2C3E50"/>
    <circle cx="365" cy="375" r="2" fill="#2C3E50"/>
    <circle cx="380" cy="375" r="2" fill="#2C3E50"/>
    
    <rect x="400" y="360" width="80" height="30" rx="4" fill="#3498DB" stroke="#2980B9" stroke-width="1"/>
    <text x="440" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Queue N</text>
    
    <rect x="490" y="360" width="80" height="30" rx="4" fill="#3498DB" stroke="#2980B9" stroke-width="1"/>
    <text x="530" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Queue N+1</text>
  </g>
  
  <!-- Bindings (Exchange to Queues) -->
  <path d="M340 320 L110 360" stroke="#E67E22" stroke-width="2" stroke-dasharray="5,5"/>
  <path d="M360 320 L200 360" stroke="#E67E22" stroke-width="2" stroke-dasharray="5,5"/>
  <path d="M360 320 L290 360" stroke="#E67E22" stroke-width="2" stroke-dasharray="5,5"/>
  <path d="M380 320 L440 360" stroke="#E67E22" stroke-width="2" stroke-dasharray="5,5"/>
  <path d="M400 320 L530 360" stroke="#E67E22" stroke-width="2" stroke-dasharray="5,5"/>
  
  <text x="600" y="350" font-family="Arial, sans-serif" font-size="10" fill="#E67E22">Bindings</text>
  <path d="M600 355 L650 355" stroke="#E67E22" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Arrows from ECS Workstations to RabbitMQ -->
  <path d="M90 180 L90 260" stroke="#34495E" stroke-width="3" marker-end="url(#arrowhead)"/>
  <path d="M190 180 L190 260" stroke="#34495E" stroke-width="3" marker-end="url(#arrowhead)"/>
  <path d="M290 180 L290 260" stroke="#34495E" stroke-width="3" marker-end="url(#arrowhead)"/>
  <path d="M490 180 L490 260" stroke="#34495E" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- Consumer Program -->
  <text x="50" y="520" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2C3E50">
    Consumer Program (Scalable)
  </text>
  
  <rect x="200" y="540" width="120" height="60" rx="8" fill="#8E44AD" filter="url(#shadow)"/>
  <text x="260" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white" font-weight="bold">Consumer</text>
  <text x="260" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Data Parser</text>
  
  <!-- Dotted box indicating scalability -->
  <rect x="350" y="540" width="120" height="60" rx="8" fill="none" stroke="#8E44AD" stroke-width="2" stroke-dasharray="8,4"/>
  <text x="410" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#8E44AD" font-weight="bold">Consumer</text>
  <text x="410" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#8E44AD">Instance 2</text>
  <text x="410" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#8E44AD">(Future)</text>
  
  <!-- Arrow from RabbitMQ to Consumer -->
  <path d="M260 440 L260 540" stroke="#34495E" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- DB2 Database (Single Instance) -->
  <text x="50" y="660" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2C3E50">
    Database
  </text>
  
  <rect x="200" y="680" width="120" height="80" rx="10" fill="url(#dbGradient)" filter="url(#shadow)"/>
  <text x="260" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white" font-weight="bold">DB2</text>
  <text x="460" y="730" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Database</text>
  
  <!-- Database icon -->
  <ellipse cx="260" cy="745" rx="15" ry="5" fill="white" opacity="0.3"/>
  <ellipse cx="260" cy="740" rx="15" ry="5" fill="white" opacity="0.5"/>
  <ellipse cx="260" cy="735" rx="15" ry="5" fill="white" opacity="0.7"/>
  
  <!-- Arrow from Consumer to DB2 -->
  <path d="M260 600 L260 680" stroke="#34495E" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- REMOVED Second DB2 instance and arrow pointing to it -->
  <!--
  <text x="50" y="810" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2C3E50">
    Database
  </text>
  <rect x="200" y="820" width="120" height="80" rx="10" fill="url(#dbGradient)" filter="url(#shadow)"/>
  <text x="260" y="850" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white" font-weight="bold">DB2</text>
  <text x="260" y="870" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Database</text>
  <ellipse cx="260" cy="885" rx="15" ry="5" fill="white" opacity="0.3"/>
  <ellipse cx="260" cy="880" rx="15" ry="5" fill="white" opacity="0.5"/>
  <ellipse cx="260" cy="875" rx="15" ry="5" fill="white" opacity="0.7"/>
  <path d="M260 750 L260 820" stroke="#34495E" stroke-width="3" marker-end="url(#arrowhead)"/>
  -->
  
  <!-- Data Flow Labels -->
  <text x="550" y="200" font-family="Arial, sans-serif" font-size="12" fill="#7F8C8D" font-style="italic">
    Data Flow Direction
  </text>
  <path d="M600 205 L600 225" stroke="#7F8C8D" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Legend -->
  <rect x="750" y="90" width="200" height="180" rx="5" fill="#F8F9FA" stroke="#BDC3C7" stroke-width="1"/>
  <text x="850" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2C3E50">
    Legend
  </text>
  
  <rect x="760" y="120" width="20" height="15" fill="url(#serverGradient)"/>
  <text x="790" y="132" font-family="Arial, sans-serif" font-size="10" fill="#2C3E50">ECS Workstation</text>
  
  <rect x="760" y="140" width="20" height="15" fill="#9B59B6"/>
  <text x="790" y="152" font-family="Arial, sans-serif" font-size="10" fill="#2C3E50">Producer Program</text>
  
  <rect x="760" y="160" width="20" height="15" fill="url(#rabbitGradient)"/>
  <text x="790" y="172" font-family="Arial, sans-serif" font-size="10" fill="#2C3E50">RabbitMQ Node</text>
  
  <rect x="760" y="180" width="20" height="15" fill="#E74C3C"/>
  <text x="790" y="192" font-family="Arial, sans-serif" font-size="10" fill="#2C3E50">Exchange</text>
  
  <rect x="760" y="200" width="20" height="15" fill="#3498DB"/>
  <text x="790" y="212" font-family="Arial, sans-serif" font-size="10" fill="#2C3E50">Message Queue</text>
  
  <rect x="760" y="220" width="20" height="15" fill="#8E44AD"/>
  <text x="790" y="232" font-family="Arial, sans-serif" font-size="10" fill="#2C3E50">Consumer Program</text>
  
  <rect x="760" y="240" width="20" height="15" fill="url(#dbGradient)"/>
  <text x="790" y="252" font-family="Arial, sans-serif" font-size="10" fill="#2C3E50">DB2 Database</text>
</svg>