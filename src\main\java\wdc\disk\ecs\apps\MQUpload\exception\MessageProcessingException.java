package wdc.disk.ecs.apps.MQUpload.exception;

public class MessageProcessingException extends RuntimeException {
    private final ErrorType errorType;
    private final boolean recoverable;

    public enum ErrorType {
        VALIDATION_ERROR(false),
        TRANSFORMATION_ERROR(true),
        BUSINESS_LOGIC_ERROR(true),
        DATABASE_ERROR(true),
        NETWORK_ERROR(true),
        UNKNOWN_ERROR(false);

        private final boolean recoverable;

        ErrorType(boolean recoverable) {
            this.recoverable = recoverable;
        }

        public boolean isRecoverable() {
            return recoverable;
        }
    }

    public MessageProcessingException(String message, ErrorType errorType) {
        super(message);
        this.errorType = errorType;
        this.recoverable = errorType.isRecoverable();
    }

    public MessageProcessingException(String message, ErrorType errorType, Throwable cause) {
        super(message, cause);
        this.errorType = errorType;
        this.recoverable = errorType.isRecoverable();
    }

    public ErrorType getErrorType() {
        return errorType;
    }

    public boolean isRecoverable() {
        return recoverable;
    }

    @Override
    public String getMessage() {
        return String.format("[%s] %s", errorType, super.getMessage());
    }
}