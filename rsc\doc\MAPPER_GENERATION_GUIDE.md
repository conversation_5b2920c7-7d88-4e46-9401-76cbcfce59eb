# Mapper Generation Guide

This guide explains how the MyBatis Mapper generation works in the MQUpload application.

## 1. Overview

The Mapper generation system automatically creates MyBatis mapper interfaces and POJO models from profile definitions. It uses Velocity templates to generate Java code that handles DB2 MERGE operations. The system supports both complete generation (profile + POJO + mapper) and POJO/mapper-only generation from existing profiles.

## 2. Required Files

### 2.1 Message Mappings
Location: `src/test/resources/mapper/message_mappings.json`
```json
{
  "messageProfiles": [
    {"tabCode": "LID", "mqMessageId": "309", "msgType": "ASCII", "tableName": "LID"},
    {"tabCode": "FTPRAW", "mqMessageId": "222", "msgType": "ASCII", "tableName": "FTP_RAW"},
    {"tabCode": "GLDRSLT", "mqMessageId": "2", "msgType": "Binary", "tableName": "GLDRSLT"}
  ]
}
```

### 2.2 Profile Files
Location: `profiles/generatedProfiles/<mqMessageId>_<tabCode>.pro`
```
*|ColumnName|DBType|IsUnique|Length|SystemType|
*|DT_ENTRY|TIMESTAMP|Y|...|System.DateTime|
*|LOT|VARCHAR|Y|...|System.String|
*|PRODUCT|VARCHAR|N|...|System.String|
```

### 2.3 Velocity Templates
- POJO Template: `src/main/resources/templates/Pojo.vm`
- Mapper Template: `src/main/resources/templates/Mapper.vm`

## 3. Generation Process

```mermaid
graph TD
    A[Database Schema] --> B[MsgProfileGenerator]
    B --> C[Profile File]
    C --> D[PojoAndMapperGenerator]
    D --> H[Generated POJO]
    D --> I[Generated Mapper]
```

## 4. Running the Generator

There are two main ways to run the generator:

### 4.1 Complete Generation (Profile + POJO + Mapper)
Use `ProfileAndMapperGenerator` for complete generation from database schema:

```java
// Main entry point in ProfileAndMapperGenerator.main()
String basePackage = "wdc.disk.ecs.apps.MQUpload";
String messageMappingsPath = "src/test/resources/mapper/message_mappings.json";
String profilesOutputDir = "profiles/generatedProfiles";
String codeOutputDir = "src/main/java/wdc/disk/ecs/apps/MQUpload";

// Define tables to process
List<TableInfo> tables = Arrays.asList(
    new TableInfo("DB2SYS", "WASHCANDELA", "WASHCANDELA", "309", false),
    new TableInfo("DB2SYS", "TDS_CHANNEL_STAT", "TDSCHANNEL", "301", false)
);

ProfileAndMapperGenerator generator = new ProfileAndMapperGenerator(
    basePackage, messageMappingsPath, profilesOutputDir, codeOutputDir, false
);

// Process each table
for (TableInfo table : tables) {
    generator.generateAll(table.schema, table.tableName, table.tabCode, table.messageId);
}
```

### 4.2 POJO and Mapper Only Generation
Use `PojoAndMapperGenerator` when profiles already exist:

```java
String messageMappingsPath = "src/test/resources/mapper/message_mappings.json";
List<String> profilePaths = Arrays.asList(
    "profiles/generatedProfiles/309_LID.pro",
    "profiles/generatedProfiles/222_FTPRAW.pro",
    "profiles/generatedProfiles/223_FTPSUM.pro"
);

PojoAndMapperGenerator generator = new PojoAndMapperGenerator(
    "wdc.disk.ecs.apps.MQUpload",
    messageMappingsPath
);

for(String profilePath : profilePaths) {
    generator.generateFromProfile(profilePath, "src/main/java/wdc/disk/ecs/apps/MQUpload");
}
```

## 5. Generated Files Structure

```
src/main/java/wdc/disk/ecs/apps/MQUpload/
├── mapper/
│   └── generatedMappers/
│       ├── FTPRAW_Mapper.java
│       ├── LID_Mapper.java
│       └── WASHCANDELA_Mapper.java
└── model/
    └── generatedModels/
        ├── FTPRAW.java
        ├── LID.java
        └── WASHCANDELA.java

profiles/generatedProfiles/
├── 222_FTPRAW.pro
├── 309_LID.pro
└── 309_WASHCANDELA.pro
```



