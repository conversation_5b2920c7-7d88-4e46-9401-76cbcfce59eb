# Mapper Generation Guide

This guide explains how the MyBatis Mapper generation works in the MQUpload application.

## 1. Overview

The Mapper generation system automatically creates MyBatis mapper interfaces from profile definitions. It uses Velocity templates to generate Java code that handles DB2 MERGE operations.

## 2. Required Files

### 2.1 Message Mappings
Location: `src/test/resources/mapper/message_mappings.json`
```json
{
  "messageProfiles": [
    {"tabCode": "222", "mqMessageId": "222", "msgType": "Binary", "tableName": "FTP_RAW"}
  ]
}
```

### 2.2 Profile Files
Location: `profiles/<mqMessageId>_<tabCode>.pro`
```
*|ColumnName|DBType|IsUnique|Length|SystemType|
*|DT_ENTRY|TIMESTAMP|Y|...|System.DateTime|
*|LOT|VARCHAR|Y|...|System.String|
*|PRODUCT|VARCHAR|N|...|System.String|
```

### 2.3 Velocity Template
Location: `src/main/resources/templates/Mapper.vm`

## 3. Generation Process

```mermaid
graph TD
    A[Profile File] --> B[TableSchemaManager]
    B --> C[TableConfigParser]
    C --> D[VelocityContext]
    D --> E[Generated Mapper]
```

### 3.1 Context Variables Available in Template
- `${package}`: Mapper package name
- `${className}`: Generated from profile name
- `${modelPackage}`: POJO model package
- `${tableName}`: DB table name
- `${uniqueColumns}`: List of key columns
- `${updateColumns}`: List of non-key columns
- `${allColumns}`: Complete column list

### 3.2 Column Object Properties
```java
class ColumnDefinition {
    String name;          // DB column name (e.g., "DT_ENTRY")
    String camelCaseName; // Java property name (e.g., "dtEntry")
    String javaType;      // Java type (e.g., "LocalDateTime")
    boolean isUnique;     // Whether column is part of unique key
}
```

## 4. Running the Generator

```java
String messageMappingsPath = "src/test/resources/mapper/message_mappings.json";
List<String> profilePaths = Arrays.asList("profiles/222_FTPRAW.pro");

PojoGenerator generator = new PojoGenerator(
    "wdc.disk.ecs.apps.MQUpload",
    messageMappingsPath
);
generator.generateFromProfile(profilePath, "src/main/java/wdc/disk/ecs/apps/MQUpload");
```

## 5. Generated Files Structure

```
src/main/java/wdc/disk/ecs/apps/MQUpload/
├── mapper/
│   └── generatedMappers/
│       └── FTPRAW_Mapper.java
└── model/
    └── generatedModels/
        └── FTPRAW.java
```

## 6. Template Usage Example

```velocity
@Insert({
    "MERGE INTO DB2SYS.${tableName} AS TARGET",
    "USING (VALUES (",
    #foreach($col in $allColumns)
        "#{${col.camelCaseName}}#if($foreach.hasNext),#end"
    #end
    ")) AS SOURCE (",
    #foreach($col in $allColumns)
        "${col.name}#if($foreach.hasNext),#end"
    #end
    ")"
})
```

