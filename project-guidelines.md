# Project: Modern Python Application

## General Instructions:

- When generating new Python code, please adhere to the existing coding style and project structure.
- All new functions, classes, and modules should have comprehensive docstrings following the Google Python Style Guide format.
- Employ modern Python features and functional programming paradigms where they enhance clarity and performance. Avoid overly complex or "clever" code.
- All code must be compatible with Python 3.13.5.
- All type hints should be valid and checked with a static type checker like MyPy or Pyright.

## Coding Style:

- **Indentation:** Use 4 spaces for indentation.
- **Line Length:** Maximum line length is 88 characters, in line with the black code formatter.
- **Naming Conventions:**
  - `snake_case` for functions, methods, variables, and modules.
  - `UPPER_CASE_SNAKE_CASE` for constants.
  - `PascalCase` for classes.
  - Private class members should be prefixed with a single underscore (`_`).
  - Protected class members (for subclass use) should be prefixed with a single underscore (`_`).
  - Avoid using `__` (double underscore) prefixes unless for name mangling in classes.
- **Type Hinting:**
  - Use type hints for all function signatures (arguments and return types).
  - Use the `|` operator for union types (e.g., `int | float`) as introduced in PEP 604.
  - For optional arguments, use `Optional[type]` from the typing module or `type | None`.
- **Docstrings:**
  - Follow the Google Python Style Guide for docstring formatting.
  - Include `Args:`, `Returns:`, and `Raises:` sections as appropriate.
- **Imports:**
  - Imports should be grouped in the following order:
    - Standard library imports.
    - Third-party library imports.
    - Local application/library specific imports.
  - Each group should be separated by a blank line.
  - Use absolute imports over relative imports where possible.

## Specific Component: `src/api/client.py`

- This file is responsible for all outgoing API requests.
- When adding new API call functions:
  - Implement robust error handling for network issues, non-2xx status codes, and unexpected response payloads.
  - Use a centralized HTTP client (e.g., an instance of `httpx.AsyncClient`) to manage connection pooling.
  - Leverage structured logging to record important information about requests and responses.
  - For GET requests prone to transient failures, consider implementing a retry mechanism with exponential backoff.

## Regarding Dependencies:

- This project uses `uv` for dependency management.
- To add a new dependency, use `uv add <package_name>`.
- To add a development-only dependency, use `uv add --dev <package_name>`.
- Avoid introducing new external dependencies unless they provide a significant advantage over the standard library or existing dependencies.
- If a new dependency is necessary, provide a brief justification for its inclusion.
- The primary dependency file is `pyproject.toml`. Do not manually edit the `uv.lock` file.

## Testing with pytest:

- Tests should be placed in the `tests/` directory, mirroring the structure of the source code.
- Test files should be named `test_*.py`.
- Test functions should be named `test_*`.
- Use pytest fixtures for setting up and tearing down test state. Define shared fixtures in `tests/conftest.py`.
- Prefer small, focused tests that follow the "Arrange, Act, Assert" pattern.
- Use `pytest.mark.parametrize` to test a function with multiple sets of inputs and expected outputs, reducing code duplication.
- For tests that require it, mock external services and dependencies using `unittest.mock`.
- Ensure tests cover not only the "happy path" but also edge cases and expected error conditions. Use `pytest.raises` to assert that expected exceptions are raised.
