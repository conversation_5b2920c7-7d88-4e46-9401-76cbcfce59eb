package wdc.disk.ecs.apps.MQUpload.processor;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Vector;

import ibm.disk.extensions.StringMethods;
import ibm.disk.profiles.DiskProfile;
import ibm.disk.profiles.ProfileException;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMessage;
import wdc.disk.ecs.apps.MQUpload.parser.BinaryMessageParser;
import wdc.disk.ecs.apps.MQUpload.parser.ParseResult;
import wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord;

/**
 * Implementation of FileProcessor for handling binary files.
 * This class reads and processes binary files, performing necessary validations
 * and handling binary data appropriately.
 */
public class BinaryFileProcessor implements FileProcessor {
    private final LogStream log;
    private final DiskProfile dbMsgIDProfile;
    private final String dbMsgIDProfileName;
    private final BinaryMessageParser binaryMessageParser;

    public BinaryFileProcessor(LogStream log, 
            DiskProfile dbMsgIDProfile, String dbMsgIDProfileName) {
        this.log = log;
        this.dbMsgIDProfile = dbMsgIDProfile;
        this.dbMsgIDProfileName = dbMsgIDProfileName;
        this.binaryMessageParser = new BinaryMessageParser(log, dbMsgIDProfile);
    }

    @Override
    public List<RabbitMessage> processFile(File file) throws IOException {
        if (!validateBinaryFile(file)) {
            return Collections.emptyList();
        }

        try {
            String hexData = readBinaryFileAsHex(file);
            if (hexData == null || hexData.trim().isEmpty()) {
                log.write(LogStream.WARNING_MESSAGE, 
                    "Empty or invalid binary file: " + file.getName());
                return Collections.emptyList();
            }

            return processBinaryData(hexData, file);
            
        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, 
                "Error processing binary file: " + file.getName() + " - " + e.getMessage());
            throw new IOException("Failed to process binary file: " + file.getName(), e);
        }
    }

    /**
     * Reads binary file and converts to hexadecimal string representation.
     * This follows the same pattern used in BinaryService for processing binary data.
     * 
     * @param file Binary file to read
     * @return Hexadecimal string representation of file content
     * @throws IOException if file reading fails
     */
    private String readBinaryFileAsHex(File file) throws IOException {
        if (!file.exists() || file.length() == 0) {
            return null;
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            StringBuffer hexBuffer = new StringBuffer();
            int data;
            
            while ((data = fis.read()) != -1) {
                // Convert each byte to 2-digit hex string with leading zeros
                hexBuffer.append(StringMethods.pad(Integer.toHexString(data), 2, '0', true));
            }
            
            return hexBuffer.toString();
        }
    }

    /**
     * Processes binary data using BinaryMessageParser.
     * Handles both single messages and packed (multi) messages.
     * 
     * @param hexData Hexadecimal string representation of binary data
     * @param file Source file for logging purposes
     * @return List of RabbitMessage objects
     */
    private List<RabbitMessage> processBinaryData(String hexData, File file) {
        List<RabbitMessage> messages = new ArrayList<>();
        
        try {
            // Use BinaryMessageParser to parse the hex data
            ParseResult parseResult = binaryMessageParser.parse(hexData);
            
            if (!parseResult.isSuccess()) {
                log.write(LogStream.ERROR_MESSAGE, 
                    "Failed to parse binary data from file: " + file.getName() + 
                    " - " + parseResult.getErrorMessage());
                return Collections.emptyList();
            }

            List<String> parsedMessages = parseResult.getParsedMessages();
            if (parsedMessages == null || parsedMessages.isEmpty()) {
                log.write(LogStream.WARNING_MESSAGE, 
                    "No messages parsed from binary file: " + file.getName());
                return Collections.emptyList();
            }

            // Extract message ID for routing
            String routingKey = extractRoutingKey(hexData);
            if (routingKey == null || routingKey.isEmpty()) {
                log.write(LogStream.ERROR_MESSAGE, 
                    "Could not determine routing key from binary file: " + file.getName());
                return Collections.emptyList();
            }

            // Create RabbitMessage for each parsed message
            for (String parsedMsg : parsedMessages) {
                RabbitMessage message = new RabbitMessage(
                    routingKey,
                    (parsedMsg + "\n").getBytes("UTF-8")
                );
                messages.add(message);
            }

            log.write(LogStream.INFORMATION_MESSAGE, 
                String.format("Successfully processed binary file: %s, generated %d messages", 
                    file.getName(), messages.size()));

        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, 
                "Error processing binary data from file: " + file.getName() + " - " + e.getMessage());
        }

        return messages;
    }

    /**
     * Extracts routing key (message ID) from binary data.
     * Uses BinaryMessageParser to extract the message ID from hex data.
     * 
     * @param hexData Hexadecimal string representation of binary data
     * @return Message ID for routing, or null if extraction fails
     */
    private String extractRoutingKey(String hexData) {
        try {
            // Use BinaryMessageParser to extract message ID
            String msgId = binaryMessageParser.extractMessageId(hexData);
            
            if (msgId != null && !msgId.trim().isEmpty()) {
                // Validate the message ID exists in profile
                DBMsgIDRecord record = binaryMessageParser.getProfileRecord(msgId);
                if (record != null) {
                    log.write(LogStream.DEBUG_MESSAGE, 
                        "Extracted message ID: " + msgId + " for table: " + record.getTableName());
                    return msgId;
                } else {
                    log.write(LogStream.WARNING_MESSAGE, 
                        "Message ID " + msgId + " not found in profile: " + dbMsgIDProfileName);
                }
            }
            
        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, 
                "Error extracting routing key from binary data: " + e.getMessage());
        }
        
        return null;
    }

    /**
     * Validates binary file before processing.
     * 
     * @param file File to validate
     * @return true if file is valid for processing
     */
    private boolean validateBinaryFile(File file) {
        if (!file.exists()) {
            log.write(LogStream.ERROR_MESSAGE, "Binary file does not exist: " + file.getPath());
            return false;
        }
        
        if (file.length() == 0) {
            log.write(LogStream.WARNING_MESSAGE, "Binary file is empty: " + file.getName());
            return false;
        }
        
        // Check minimum size for binary message (at least msgId + correlId = 16 hex chars = 8 bytes)
        if (file.length() < 8) {
            log.write(LogStream.ERROR_MESSAGE, 
                "Binary file too small to contain valid message: " + file.getName());
            return false;
        }
        
        return true;
    }
}

