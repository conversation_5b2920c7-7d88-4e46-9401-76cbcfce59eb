#!/bin/bash
# Set application variables  
APP_HOME=C:/Development/TFS_SourceCode/ECS/SZ_GO/Common/Dev/SFTPSend_Hao
# Fix classpath separator for Windows MINGW environment
CLASSPATH="${APP_HOME}/target/SFTPSend-1.0-SNAPSHOT.jar;D:/RabbitMQ/Dependiencies/*"
CONFIG_FILE=${APP_HOME}/src/main/resources/db-compare-config.properties


# Check if hours back argument is provided
HOURS_BACK=10
if [ $# -gt 0 ]; then
    HOURS_BACK=$1
fi

echo "Starting DB Record Count Comparison..."
echo "Using config file: ${CONFIG_FILE}"
echo "Using jar file: ${APP_HOME}/target/SFTPSend-1.0-SNAPSHOT.jar"
echo "Hours back: ${HOURS_BACK}"

#validate jar file exist
if [ ! -f "${APP_HOME}/target/SFTPSend-1.0-SNAPSHOT.jar" ]; then
    echo "Error: SFTPSend-1.0-SNAPSHOT.jar not found in ${APP_HOME}/target"
    exit 1
fi

# Run the DBRecordCountComparator
java -cp "${CLASSPATH}" wdc.disk.ecs.apps.dbcompare.DBRecordCountComparator "${CONFIG_FILE}" "${HOURS_BACK}" 


echo "Comparison completed."
