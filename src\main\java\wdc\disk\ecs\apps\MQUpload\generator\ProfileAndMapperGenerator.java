package wdc.disk.ecs.apps.MQUpload.generator;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class ProfileAndMapperGenerator {

    private final String profilesOutputDir;
    private final String codeOutputDir;
    private final boolean isBinary;
    
    // Generators
    private final MsgProfileGenerator profileGenerator;
    private final PojoAndMapperGenerator pojoAndMapperGenerator;
    
    public ProfileAndMapperGenerator(String basePackage, String messageMappingsPath, 
                                    String profilesOutputDir, String codeOutputDir, 
                                    boolean isBinary) {
        this.profilesOutputDir = profilesOutputDir;
        this.codeOutputDir = codeOutputDir;
        this.isBinary = isBinary;
        
        this.profileGenerator = new MsgProfileGenerator();
        this.pojoAndMapperGenerator = new PojoAndMapperGenerator(basePackage, messageMappingsPath);
    }
    
    /**
     * Generate profile, POJO, and mapper for a specific table
     */
    public void generateAll(String schema, String tableName, String tabCode, String messageId) throws Exception {
        // Step 1: Generate profile
        String profileFileName = messageId + "_" + tabCode + ".pro";
        String profilePath = profilesOutputDir + File.separator + profileFileName;
        
        // Ensure profiles directory exists
        File profilesDir = new File(profilesOutputDir);
        if (!profilesDir.exists()) {
            profilesDir.mkdirs();
        }
        
        // Generate profile
        profileGenerator.generateProfiles(schema, tableName, profilePath, isBinary);
        System.out.println("Profile generated: " + profilePath);
        
        // Step 2: Generate POJO and Mapper
        try {
            pojoAndMapperGenerator.generateFromProfile(profilePath, codeOutputDir);
            System.out.println("POJO and Mapper generated for: " + tableName);
            System.out.println("\n");
        } catch (IOException e) {
            throw new RuntimeException("Failed to generate POJO and Mapper", e);
        }

    }
    
 
    /**
     * Main method to run the generator
     * 
     * To run this generator, follow these steps:
     * 1. Prepare message_mappings.json:
     *    This file should contain mappings between database columns and message fields.
     *    It should be located at "src/test/resources/mapper/message_mappings.json".
     * 
     * 2. Define tables in the List<TableInfo>:
     *    Add TableInfo objects for each table you want to process.
     *    Each TableInfo should include the schema, table name, and tab code.
     *    Example: new TableInfo("DB2SYS", "WASHCANDELA", "309")
     * 
     * @param args Command line arguments
     */
    public static void main(String[] args) {
        // Configuration
        String basePackage = "wdc.disk.ecs.apps.MQUpload";
        String messageMappingsPath = "src/test/resources/mapper/message_mappings.json";
        String profilesOutputDir = "profiles\\generatedProfiles";
        String codeOutputDir = "src/main/java/wdc/disk/ecs/apps/MQUpload";
        boolean isBinary = false;
        
        // Tables to process
        List<TableInfo> tables = new ArrayList<>();

        String schema = "DB2SYS";
        // tables.add(new TableInfo(schema, "TDS_CLUSTER_DATA", "TDSCLUSDATA", "305"));
        // tables.add(new TableInfo(schema, "TDS_CHANNEL_STAT", "TDSCHANNEL", "301"));
        // tables.add(new TableInfo(schema, "TDS_CLUSTER", "TDSCLUSTER", "304"));
        
        // tables.add(new TableInfo(schema, "TDS_ENG", "TDSENG", "310"));
        // tables.add(new TableInfo(schema, "TDS_GRADE", "TDSGRADE", "303"));
        // tables.add(new TableInfo(schema, "TDS_HEAD_STAT", "TDSHEADSTAT", "302"));
        // tables.add(new TableInfo(schema, "TDS_SCAN", "TDSSCAN", "300"));
        // tables.add(new TableInfo(schema, "TDS_SITEMARK_CLUS", "TDSSITEMARK", "306"));

        //tables.add(new TableInfo(schema, "BANDRSLT", "BANDRSLT", "8", true));
        tables.add(new TableInfo(schema, "GLDRSLT", "GLDRSLT", "2", true));
        
        
        // Create generator
        // Create an instance of the main generator, which is capable of performing the full
        // end-to-end process: generating a profile file and then the corresponding POJO and Mapper.
        ProfileAndMapperGenerator profileAndMapperGenerator = new ProfileAndMapperGenerator(
            basePackage, messageMappingsPath, profilesOutputDir, codeOutputDir, isBinary
        );

        // Create an instance of a specialized generator that only handles creating POJOs and Mappers
        // from an existing profile file. This is used for cases where the profile generation step can be skipped.
        PojoAndMapperGenerator pojoAndMapperGenerator = new PojoAndMapperGenerator(
            basePackage, messageMappingsPath
        );
       
        
        // Process each table
        for (TableInfo table : tables) {
            try {
                if (table.onlyPOJOAndMapper) {
                    pojoAndMapperGenerator.generateFromProfile(
                        "profiles\\generatedProfiles\\" + table.messageId + "_" + table.tabCode + ".pro", 
                        codeOutputDir
                    );
                }else{
                    profileAndMapperGenerator.generateAll(table.schema, table.tableName, table.tabCode, table.messageId);
                }

            } catch (Exception e) {
                System.err.println("Error processing table " + table.tableName + ": " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        System.out.println("Generation complete.");
    }
    
    private static class TableInfo {
        private String schema;
        private String tableName;
        private String tabCode;
        private String messageId;
        private boolean onlyPOJOAndMapper;

        public TableInfo(String schema, String tableName, String tabCode, String messageId) {
            this(schema, tableName, tabCode, messageId, false);
        }

        public TableInfo(String schema, String tableName, String tabCode, String messageId, boolean onlyPOJOAndMapper) {
            this.schema = schema;
            this.tableName = tableName;
            this.tabCode = tabCode;
            this.messageId = messageId;
            this.onlyPOJOAndMapper = onlyPOJOAndMapper;
        }

 
    }
}