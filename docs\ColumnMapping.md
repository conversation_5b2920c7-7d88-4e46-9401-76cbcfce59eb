
## Profile column definition:
ColumnName: This is the unique identifier or label given to a specific column within a database table. 

DBColType: This specifies the data type that the database system uses to store the information in this column (e.g., integer,char…).

DBColLen: This indicates the defined length or size of the column as specified in the database schema. 

SysType: This refers to the corresponding data type used by the system or programming language that interacts with the database (e.g., System.Int32, System.String, System.DateTime in .NET).

BinLength: This is the size of the data in bytes when it is represented in a binary format. For fixed-size data types, this often matches the DBColLen.

ColNo: This is the sequential number or position of the column within the table structure or a defined data layout, typically starting from 0 or 1.

NULLS: This indicates whether the column is allowed to contain null values. 'Y' usually means it can be null, while a blank or 'N' means it requires a value.

StartIdx: This specifies the starting index or offset (usually in bytes) of the column's data within a larger record or data block, particularly relevant when data is stored or processed in a contiguous binary format.

ValueIdx: This is another index, indicating the position of the column's value within pds file.

---
## DB and System Type Mapping Relation:
| DBColumnType | SystemType      | DBColumnLength | BinaryLength | Notes                                                                                                                                                                                                                                                |
|--------------|-----------------|----------------|--------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Timestamp    | System.DateTime | 4              | 4            | Consistent mapping and size.|
| Char         | System.String   | Variable       | Variable     | Length varies based on the specific column definition |
| Varchar      | System.String   | Variable       | Variable     | Consistent mapping and size.    |
| SmallInt     | System.Int16    | 2              | 2            | Consistent mapping and size  |
| Double       | System.Double   | 8              | 8            | Consistent mapping and size. |
| Integer      | System.Int32    | 4              | 4            | Consistent mapping and size, except for DISKSEQUENCE.  |
| Integer      | System.Int64    | 4              | 2            | For DISKSEQUENCE, System.Int64 maps to Integer (DB), but BinaryLength is 2, while DBColumnLength is 4.      |


